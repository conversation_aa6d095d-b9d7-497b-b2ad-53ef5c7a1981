#include "SerialMonitor.h"
#include <iostream>
#include <string>
#include <cstdlib>
#include <chrono>
#include <thread>
#include <unistd.h>
#include <sys/inotify.h>
#include <sys/select.h>

SerialMonitor::SerialMonitor() : running(false) {
    // 串口到LED的映射关系
    serialPortToLED = {
        {"/dev/ttymxc6", "/sys/class/leds/led5/brightness"},
        {"/dev/ttymxc7", "/sys/class/leds/led3/brightness"},
        {"/dev/ttymxc1", "/sys/class/leds/led1/brightness"},
        {"/dev/ttymxc3", "/sys/class/leds/led6/brightness"}
    };
}

SerialMonitor::~SerialMonitor() {
    stop();
}

// 使用 inotify 实时监控
void SerialMonitor::monitorPort(const std::string& portPath) {
    int inotifyFd = inotify_init();
    if (inotifyFd == -1) {
        std::cout << "无法创建 inotify 实例: " << portPath << std::endl;
        return;
    }

    int wd = inotify_add_watch(inotifyFd, portPath.c_str(), IN_ACCESS | IN_MODIFY);
    if (wd == -1) {
        std::cout << "无法监控 " << portPath << std::endl;
        close(inotifyFd);
        return;
    }

    std::cout << "开始监控: " << portPath << std::endl;

    char buffer[4096];
    while (running) {
        fd_set readfds;
        FD_ZERO(&readfds);
        FD_SET(inotifyFd, &readfds);

        struct timeval timeout;
        timeout.tv_sec = 1;
        timeout.tv_usec = 0;

        int result = select(inotifyFd + 1, &readfds, nullptr, nullptr, &timeout);
        if (result > 0 && FD_ISSET(inotifyFd, &readfds)) {
            ssize_t length = read(inotifyFd, buffer, sizeof(buffer));
            if (length > 0) {
                std::string ledPath = serialPortToLED[portPath];
                std::cout << "检测到串口 " << portPath << " 有数据交互，LED亮灭交替" << std::endl;

                // 亮灭交替效果
                controlLED(ledPath, true);   // 先点亮
                std::this_thread::sleep_for(std::chrono::milliseconds(200)); // 亮200ms
                controlLED(ledPath, false);  // 再熄灭
            }
        }
    }

    inotify_rm_watch(inotifyFd, wd);
    close(inotifyFd);
}

// 启动监控
void SerialMonitor::start() {
    if (running) {
        return;
    }

    running = true;
    std::cout << "启动串口监控..." << std::endl;

    // 为每个串口创建监控线程
    for (const auto& pair : serialPortToLED) {
        const std::string& portPath = pair.first;
        const std::string& ledPath = pair.second;

        std::cout << "监控串口: " << portPath << " -> LED: " << ledPath << std::endl;

        monitorThreads.emplace_back([this, portPath]() {
            monitorPort(portPath);
        });
    }
}

// 停止监控
void SerialMonitor::stop() {
    if (!running) return;
    
    running = false;
    
    for (auto& thread : monitorThreads) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    
    monitorThreads.clear();
}

bool SerialMonitor::isRunning() const {
    return running;
}

// LED控制函数
void SerialMonitor::controlLED(const std::string& ledPath, bool turnOn) {
    // 使用echo命令写入LED控制文件
    std::string command = "echo " + std::to_string(turnOn ? 1 : 0) + " > " + ledPath;
    int result = system(command.c_str());

    if (result == 0) {
        std::cout << "LED " << (turnOn ? "亮" : "灭") << " (" << ledPath << ")" << std::endl;
    } else {
        std::cout << "无法控制LED (路径: " << ledPath << ")" << std::endl;
    }
}

#pragma once
#include "IDriver.h"

#include <string>
#include <map>
#include <vector>
#include <thread>
#include <mutex>
#include <atomic>

#include "ICommSerialPort.h"
#include "ACommFactory.h"

namespace DRIVER
{
	enum EMeterType
	{
		eMeterColdWater = 0x10,
		eMeterHotWater = 0x11,
		eMeterDirectDrinkingWater = 0x12,
		eMeterReclaimedWater = 0x13,
		eMeterHeatHot = 0x20,
		eMeterHeatCold = 0x21,
		eMeterHeat = 0x22,
		eMeterFlow = 0x30,
		eMeterElectricity = 0x40,
		eMeterCustomStart = 0x41,
		eMeterCustomEnd = 0x49
	};

	enum EUnitType
	{
		eUnitWh = 0x02,
		eUnitKWh = 0x05,
		eUnitMWh = 0x08,
		eUnitKWhx100 = 0x0a,
		eUnitJ = 0x01,
		eUnitKJ = 0x0b,
		eUnitMJ = 0x0e,
		eUnitGJ = 0x11,
		eUnitGJx100 = 0x13,
		eUnitQ = 0x14,
		eUnitKW = 0x17,
		eUnitMW = 0x1a,
		eUnitL = 0x29,
		eUnitStere = 0x2c,		// m^3
		eUnitLPerH = 0x32,		// L/h
		eUnitSterePerH = 0x35	// m^3 /h
	};

	struct SChannelProtocol : public IProtocol
	{
		std::string device; 
		int baud; 
		uint8_t dataBit; 
		uint8_t stopBit; 
		char parity;
	};

	struct SPointProtocol : public IProtocol
	{
		EMeterType meterType;
		std::string meterAddress;
		std::string deviceId;
		//bool isTotalUsage;

		std::string toString() const
		{
			std::string key;
			key += std::to_string(meterType);
			key += ".";
			key += meterAddress;
			//key += ".";
			//key += std::to_string(isTotalUsage);
			return key;
		}
	};

	class CDriverEMSBoxCJT188_build202528 : public IDriver
	{

	private:
		IDriverCallback* cb_;

		uint8_t request_[16];
		
		COMM::ICommSerialPort* serialPortClient_;

		inline void initialRequest(uint8_t* request);
		inline void setMeterType(uint8_t* request, EMeterType type);
		inline void setMeterAddress(uint8_t* request, std::string number);
		inline void setCheckCode(uint8_t* request);

		bool valid(uint8_t* response, int responseLen);
		void getMonthUsage(uint8_t* response, float& monthUsage);
		void getTotalUsage(uint8_t* response, float& totalUsage);
		size_t getTime(uint8_t* response);

		bool readFromDevice(const SPointProtocol* pp, SValue& value);

	public:
		CDriverEMSBoxCJT188_build202528();
		virtual ~CDriverEMSBoxCJT188_build202528();

		//IDriver
		virtual bool open(const SProtocolNode& pn);
		virtual bool close(const SProtocolNode& pn);
		virtual EStatusCode control(const IProtocol* const channelProtocol, const IProtocol* const deviceProtocol, const IProtocol* const pointProtocol, const SControlInfo& controlInfo);
		virtual EStatusCode controlSet(const std::unordered_map<std::string, std::vector<SControlSetInfo>>& controlValues);
		virtual void setCallback(IDriverCallback* cb);
		virtual EStatusCode poll(const SProtocolNode& pn);
		virtual std::string getPointKey(const IProtocol* const channelProtocol, const IProtocol* const deviceProtocol, const IProtocol* const pointProtocol);
		virtual IProtocol* createProtocol(EProtocolType type, OriginProtocol& originProtocol);
		virtual void destoryProtocol(EProtocolType type, IProtocol* protocol);
		virtual void setFlag(EFlag flag);
	};

} //namespace DRIVER end


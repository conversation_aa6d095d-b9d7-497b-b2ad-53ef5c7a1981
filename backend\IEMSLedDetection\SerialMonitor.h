#pragma once

#include <vector>
#include <string>
#include <thread>
#include <atomic>
#include <map>

class SerialMonitor {
private:
    std::map<std::string, std::string> serialPortToLED; // 串口->LED路径映射
    std::vector<std::thread> monitorThreads;
    std::atomic<bool> running;

    // 使用 inotify 实时监控
    void monitorPort(const std::string& portPath);

    // LED控制
    void controlLED(const std::string& ledPath, bool turnOn);

public:
    SerialMonitor();
    ~SerialMonitor();

    // 启动监控
    void start();

    // 停止监控
    void stop();

    bool isRunning() const;
};

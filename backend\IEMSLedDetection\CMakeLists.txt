# CMakeList.txt : CMake project for IEMSLedDetection, include source and define
# project specific logic here.
#

project (IEMSLedDetection VERSION 1.0)

set(EXECUTABLE_OUTPUT_PATH ${OUTPUT_DIR})
set(CMAKE_BINARY_DIR ${BUILD_DIR}/${PROJECT_NAME})

# 设置C++标准
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

if(WIN32)
    # Windows平台配置（如果需要）
elseif(UNIX)
    # Linux平台配置
    find_package(Threads REQUIRED)
endif()

include_directories(
    ${PROJ_ROOT_DIR}
)

link_directories(
    ${OUTPUT_DIR}
)

# 源文件
aux_source_directory(./ SRCS)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SRCS})

# 链接库
if(UNIX)
    # Linux平台需要链接pthread库
    target_link_libraries(${PROJECT_NAME} ${CMAKE_THREAD_LIBS_INIT})
elseif(WIN32)
    # Windows平台链接库（如果需要）
    # target_link_libraries(${PROJECT_NAME} ...)
endif()

# 编译选项
if(UNIX)
    # Linux平台编译选项
    target_compile_options(${PROJECT_NAME} PRIVATE -Wall -Wextra)
    
    # ARM32平台特定选项（如果需要）
    if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm")
        target_compile_options(${PROJECT_NAME} PRIVATE -march=armv7-a)
    endif()
endif()

# 安装规则（可选）
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

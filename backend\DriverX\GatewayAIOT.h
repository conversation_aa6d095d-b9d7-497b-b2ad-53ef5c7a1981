#pragma once
#ifndef _GATEWAY_AIOT_H_
#define _GATEWAY_AIOT_H_

#include <iostream>
#include <map>
#include <set>
#include <string>

#include "httplib.h"
#include "spdlog/sinks/rotating_file_sink.h"

#include "Gateway.h"
#include "AGatewayFactory.h"
#include "ICommHttp.h"
#include "ICommMqtt.h"
#include "ICommModbus.h"
#include "ACommFactory.h"
#include "LocalDataCaching.h"
#include "ISystemSetting.h"
#include "LogCaching.h"
#include "ConfigurationCaching.h"
#include "OpcUaServer.h"
#include "yyjson.h"
#include "iEMS-COM4.h"

#define DRIVER_PLUGIN_PATH "./Driver"
#define WEB_PATH "./scanner_web"
#define LOG_PATH "./logs"
#define CONFIG_PATH "./AIOTConfig.json"
#define USERINFO_PATH "./UserInfo.json"
#define MODBUS_MAPPING_TABLE_PATH "./ModbusMappingTable.json"
#define GATEWAY_FLAG "./aiotflag"
#define GATEWAY_VERSION	"./file_list.xml"
//#define GATEWAY_SERIALNUMBER "./SerialNumber"
#define MANAGE_CONFIG_PATH "./ManageConfig.json" //运维平台配置

#define CLOUD_CONFIG_PATH "./cloudConfig/"
#define CRT_PATH "./crt/"
#define MANAGE_CRT_PATH "./manageCrt/"

#define TOPIC_TYPE_CONFIG		"config"
#define TOPIC_TYPE_CONTROL		"control/request"
#define TOPIC_TYPE_CONTROLSET	"controlSet/request"
//#define TOPIC_TYPE_REPORT_ALL	"rtdb/overallrequest"
#define TOPIC_TYPE_DATA_CALL_ALL	"supaiot/data/call"
#define TOPIC_TYPE_DATA_CALL_SGL	"supaiot/data/call/"

//hypcon-iot -> supaiot
#define API_VERSION				"/supaiot/api/config/edge-relation/version?clientID="
#define API_DOWNLOAD_CONFIG		"/supaiot/api/config/edge-relation/configuration?clientID="
#define API_DEVICE_CONFIG		"/supaiot/api/config/device/%7Bid%7D?deviceIds="
#define API_EVENT_FILE_PATH		"/supaiot/api/device-resource/"
#define API_CLOUD_TIME			"/supaiot/api/config/common/time"

//version file
#define VERSION_FILE_NAME	"version"
#define PACKGE_INFO_FILE	"packageInfo"

#define GENERATE_MULTI_GATEWAY_FILE_PATH(launchPath, driverXId, fileName) (launchPath + "/../config/gateway/"+std::string(driverXId)+"/"+std::string(fileName))
#define GENERATE_MULTI_GATEWAY_LOG_PATH(launchPath, driverXId, channelName) (launchPath + "/../logs/gateway/"+std::string(driverXId)+"/"+std::string(channelName)+"/")

namespace GATEWAY
{
	enum EGatewayEnv
	{
		Normal,
		E500,
		iEMSCOM4,
		Multi
	};
	std::map<EGatewayEnv, std::string> envToString = {
		{ EGatewayEnv::Normal, "normal"},
		{ EGatewayEnv::E500, "e500"},
		{ EGatewayEnv::iEMSCOM4, "iEMS-COM4"},
		{ EGatewayEnv::Multi, "multi"}
	};
	std::map<std::string, EGatewayEnv> stringToEnv = {
		{ "normal", EGatewayEnv::Normal},
		{ "e500", EGatewayEnv::E500},
		{ "iEMS-COM4", EGatewayEnv::iEMSCOM4},
		{ "multi", EGatewayEnv::Multi}
	};

	enum EConfigurationSource
	{
		ECloud,
		ELocal
	};
	std::map<EConfigurationSource, std::string> collectTypeToString = {
		{EConfigurationSource::ECloud, "cloud"},
		{EConfigurationSource::ELocal, "local"}
	};

	struct SAIOTEvent
	{
		std::string deviceId;
		std::string deviceName;
		std::string uuid;
		SEventSptr eventSptr;
	};
	using SAIOTEventSptr = std::shared_ptr<SAIOTEvent>;

	struct SAttribute
	{
		std::string code;
		std::string name;
		std::string description;
		std::string pointType;
		std::string type;
		double minValue;
		double maxValue;
		double added;
		double coefficient;
		std::map<std::string, std::string> value;
		int pos; //原型中的点位顺序
		SAttribute() : minValue(0.0), maxValue(0.0), added(0.0), coefficient(1.0), pos(0) {}
	};

	struct SClassProduct
	{
		std::string classId;
		//uint64_t creationTime;
		//uint64_t modificationTime;
		std::string name;
		std::map<std::string, SAttribute> attributes;
		//std::string type;
		//int version;
		//std::string projectId;
		//std::string systemVersion;
		//std::string classType;
	};

	struct SClassLink
	{
		enum SArgumentLevle
		{
			EUnknow = 0,
			EChannel,
			EDevice,
			EPointAddress,
			EPointDatType
		};

		struct SArgument
		{
			std::string name;
			std::string label;
			std::string description;
			std::string type;
			std::string defaultValue;
			std::string inputType;
			Json::Value items;
			int option;
			bool visible;
			bool required;
			SArgumentLevle level = SArgumentLevle::EUnknow;
		};

		struct SConnectionArg
		{
			std::string name;
			std::string label;
			std::string description;
			std::string type;
			std::string defaultValue;
			std::string inputType;
			Json::Value items;
			int option;
			bool visible;
			bool required;
			bool useIEMSCom;
			SArgumentLevle level = SArgumentLevle::EUnknow;
		};

		std::string name;
		std::string argumentAscription;
		std::map<std::string, std::string> driverFile;
		std::vector<SConnectionArg> connection;
		std::vector<SArgument> arguments;
		std::vector<SAttribute> attributes;
		uint64_t creationTime;
		uint64_t modificationTime;
		//std::string type;
		std::string classId;
		//int version;
		//std::string projectId;
		//std::string systemVersion;
		//std::string classType;
		std::string scriptType;
		std::string javascriptExpresstion;
		std::string argumentStr;   //json�ַ�������ǰ��չʾ����ʹ��
		std::string connectionStr; //json�ַ�������ǰ��չʾ����ʹ��

		std::string iemsProtocolClass;
		std::string iemsProtocolInterfaceType;
	};

	struct SClassGateway
	{
		std::string name;
		std::vector<SAttribute> attributes;
		//uint64_t creationTime;
		//uint64_t modificationTime;
		//std::string type;
		std::string classId;
		//int version;
		//std::string projectId;
		//std::string systemVersion;
		//std::string classType;
	};

	struct SObjectDevice
	{
		std::string classId;
		//        point id        argument table     
		std::map<std::string, std::map<std::string, std::string>> protocol;
		//std::string areaId;
		//uint64_t creationTime;
		//uint64_t modificationTime;
		std::string name;
		std::string fatherId;
		std::string id;
		//int version;
		//std::string projectId;
		//std::string systemVersion;
		//std::string classType;
	};

	struct SObjectLink
	{
		std::string classId;
		std::map<std::string, std::string> connection;
		std::string className;
		//uint64_t creationTime;
		//uint64_t modificationTime;
		std::string name;
		std::string fatherId;
		std::string id;
		//int version;
		//std::string projectId;
		//std::string systemVersion;
		//std::string classType;
	};

	struct SObjectGateway
	{
		std::string classId;
		std::string className;
		std::string mqttPassword;
		std::string mqttClientID;
		std::string mqttUsername;
		//uint64_t creationTime;
		//uint64_t modificationTime;
		std::string name;
		std::string id;
		//int version;
		//std::string projectId;
		//std::string systemVersion;
		//std::string classType;

		std::string projectName;		// 项目名
		bool cloudConnectionStatus;   	// 平台地址连接状态
		bool cloudConfigGetStatus;		// 获取平台配置状态
	};

	// modbus start
	struct SModbusParam
	{
		int addr;
		std::string pointType; //aio,ai,di,do,...
		std::string dataType;
		std::string name;
		SValue value;

		SModbusParam() : addr(-1) {}
	};
	using ModbusMappingTable = std::unordered_map<std::string, std::unordered_map<std::string, std::unordered_map<std::string, SModbusParam>>>;
	using ModbusAddrPointKeyMappingTable = std::unordered_map<int, std::string>;
	// modbus end

	struct SCloudMessage
	{
		std::string cloudID;
		std::string topic;
		std::string payload;
	};
	using SCloudMessageSptr = std::shared_ptr<SCloudMessage>;

	class CMqttClient;
	struct SDataDistribution
	{
		std::string mqttUrl;
		std::string mqttUsername;
		std::string mqttPassword;
		std::string clientId;
		CMqttClient* mqttClient;
		std::string resourceServer;
	};

	struct SGatewayInfoReportConfig {
		bool isReport;
		bool basic;
		bool runtime;
		uint64_t interval;
		SGatewayInfoReportConfig() :isReport(true), basic(true), runtime(false),
			interval(600) {}
	};

	struct SGatewayManageReportConfig {
		bool isReport;                      //是否上报
		int interval;                       //上报周期 (s)
		std::string alarmTopic;	            //报警主题
		std::string infoTopic;			    //运行信息主题
		int cpuAlarm;				        //cpu报警阈值
		int cpuAlarmSamplingInterval;       //cpu采样周期 (s)
		int cpuAlarmPercent;                //cpu报警占比 (60则表示上报周期内60%的采样数据超过报警阈值则产生cpu报警)
		int memoryAlarm;                    //内存报警阈值
		int diskAlarm;                      //磁盘 (DriverX所在分区) 报警阈值
		SDataDistribution managePlatform;   //连接配置
		SGatewayInfoReportConfig girc;      //基本/运行信息上送配置
		SGatewayManageReportConfig() : isReport(false), interval(3600), cpuAlarm(60), memoryAlarm(60), diskAlarm(60) {}
	};

	struct SFileInfo
	{
		std::string name;
		uint64_t modificationTime;
		uint64_t size;
	};

	struct SMqttResource
	{
		std::string type;	// cmd type
		bool res;			// succed/failed
		std::string data;	// err msg
	};

	class CGatewayAIOT : public CGateway
	{
		friend class CMqttClient;

	private:
		std::atomic_bool running_;
		std::string versionUri_;
		std::string configUri_;
		std::string eventFilePath_;
		std::string cloudTimeUri_;
		std::string serialNumberFile;
		int64_t timeDifference_;
		SDataDistribution mainPlatform_;
		std::vector<SDataDistribution> dataDistributionVec_;
		std::mutex mqttListMutex_;

		// 运维平台
		void loadManageConfig();
		std::thread* uploadManageThread_;
		void uploadManageThread();
		SGatewayManageReportConfig gmrc_;

		// platform config change 
		bool killSelf_;
		// regularly report interval
		int64_t regularlyReportInterval_;
		bool recordReportData_;


		// func
		void loadGatewayConfig();

		// comm
		COMM::ICommHttp* httpClient_;

		// modbus
		bool enableModbusServer_;
		COMM::ICommModbus* modbusTcpClient_;
		ModbusMappingTable modbusMappingTable_;
		ModbusAddrPointKeyMappingTable modbusCoilAddrPointKeyMappingTable_;
		ModbusAddrPointKeyMappingTable modbusRegisterAddrPointKeyMappingTable_;
		void loadModbusMappingTable(ModbusMappingTable& mmt, const std::string& file);
		void saveModbusMappingTable(const ModbusMappingTable& mmt, const std::string& file);
		int getModbusAddr(ModbusMappingTable& mmt, const std::string& channelName, const std::string& deviceName, const std::string& pointName);
		void onWriteModbusRequest(std::vector<uint8_t>& req);

		// main
		std::shared_ptr<spdlog::logger> logger_;
		std::mutex cloudMessageQueueMutex_;
		std::queue<SCloudMessageSptr> cloudMessageQueue_;
		std::condition_variable mainCondVar_;
		std::thread* mainThread_;
		void mainThread();

		// data
		std::mutex dataSetQueueMutex_;
		std::condition_variable dataSetQueueCondVar_;
		std::queue<SDataSet> dataSetQueue_;
		std::thread* sendDataThread_;
		void sendDataThread();
		void sendAllData();
		void sendDataToMqtt(const std::string deviceId, const Json::Value& pkg);
		void sendCacheDataToMqtt();

		// event
		std::mutex eventQueueMutex_;
		std::queue<SAIOTEventSptr> eventQueue_;
		std::condition_variable eventCondVar_;
		std::thread* reportEventThread_;
		void reportEvent(SAIOTEventSptr aiotEventSptr, CMqttClient* mqttClient);
		bool uploadEventFile(SAIOTEventSptr aiotEventSptr, const std::string& resourceServer);
		void reportEventThread();

		// control
		bool enableCloudControl_;
		std::vector<std::string> deviceIdVec_;
		std::vector<std::string> ChannelIdVec_;

		// config
		SObjectGateway sog_;
		std::map<std::string, DRIVER::SChannelConfig> slcs_;
		std::map<std::string, int> productVerTable_;
		std::map<std::string, int> deviceVerTable_;

		//write localConfig
		template<typename T>
		int writeDriverXConfig(const std::string& key, const T& value);
		std::mutex driverXConfigMutex_;

		//systemsetting
		ISystemSetting* systemSetting_;
		std::string systemSettingFloderPath_;

		//gatewayinfo report
		SGatewayInfoReportConfig girc_;
		std::thread* uploadInfoThread_;
		void uploadInfoThread();
		bool uploadInfoNow_;
		void getUploadInfo(int64_t nowTime, bool basic, bool runtime, Json::Value& pkg);

		//log
		CLogCaching *lc_;
		bool insertLog(ELogType type, const std::string& content);

		//network
		bool enableNetWorkChange_;

		// env
		EGatewayEnv env_;
		Json::Value getLocalEnv();
		bool checkEnv(EGatewayEnv env);
		uint64_t processStartTime_;
		uint64_t totalLinkCount_;
		uint64_t totalDeviceCount_;
		uint64_t innerPointCount_;
		uint64_t normalPointCount_;

		// multi gateway config
		bool isMultiGatewayEnv;
		std::string driverXId;
		std::string listenPort;
		std::string managerPort;
		std::string launchPath;

		// log config
		int logFileSize_;
		int logFileCount_;
		int logBaseSize_;
		int logLevel_;

		// configuration 采集模式
		EConfigurationSource configSource_ = EConfigurationSource::ECloud;
		std::map<std::string, SClassLink> localLinkTemplates_;
		std::map<std::string, DRIVER::SData> globalDatas_;
		SIEMSConfig iemsConfig_;
		bool loadIEMSCfg();
		void loadLocalDriverTemplate();
		CConfigurationCaching configurationCaching_;
		std::unordered_map<std::string, std::unordered_map<std::string, std::set<std::string>>> opcuaPointsMap_;
		std::map<std::string, DRIVER::SData> opcuaCacheMap_;
		std::thread* updateDataToOpcuaServerThread_;
		void updateDataToOpcuaServerThread();
		void sendDataToOpcuaServer(std::string& pointCode, DRIVER::SData& data);
		COpcUaServer opcServer_;
		void opcuaServerWriteCallBack(const std::tuple<std::string, std::string, std::string>& pointInfo, const SubData& value, const UA_NodeId* nodeId);
		Json::Value localConfigJson_;
		bool localConfigDriverRunningState_ = false;
		bool localConfigOPCUARunningState_ = false;
		std::string opcuaServerEndpoint_ = "0.0.0.0:4840";

		std::mutex localConfigRunningStateMtx_;
		void getLocalConfigRunningState();
		void setLocalConfigRunningState();
		bool startLocalConfigWork();
		void startLocalConfigOPCUAWrok();
		// channelname - devicename - pointname - sdata
		//std::unordered_map<std::string, std::unordered_map<std::string, std::unordered_map<std::string, DRIVER::SData>>> configurationDatas_;
		std::string yyjson_val_to_string(yyjson_val* val);
		void httpConfigGetConfigurationSource(const httplib::Request& req, httplib::Response& res);
		void httpConfigSetConfigurationSource(const httplib::Request& req, httplib::Response& res);
		void httpConfigSetDeviceTemplate(const httplib::Request& req, httplib::Response& res);
		void httpConfigGetDeviceTemplate(const httplib::Request& req, httplib::Response& res);
		void httpConfigDelDeviceTemplate(const httplib::Request& req, httplib::Response& res);
		void httpConfigGetConfiguration(const httplib::Request& req, httplib::Response& res);
		void httpConfigGetCfgPoints(const httplib::Request& req, httplib::Response& res);
		void httpConfigSetConfigurationTemplate(const httplib::Request& req, httplib::Response& res);
		void httpConfigGetConfigurationTemplate(const httplib::Request& req, httplib::Response& res);
		void httpConfigDelConfigurationTemplate(const httplib::Request& req, httplib::Response& res);
		void httpConfigGetLinkTemplate(const httplib::Request& req, httplib::Response& res);
		void httpConfigSetLocalConfiguration(const httplib::Request& req, httplib::Response& res);
		bool loadLocalConfig(SObjectGateway& sog, std::map<std::string, DRIVER::SChannelConfig>& sccs);
		void httpConfigStartLocalConfigurationWork(const httplib::Request& req, httplib::Response& res);
		void httpConfigStopLocalConfigurationWork(const httplib::Request& req, httplib::Response& res);
		void httpConfigGetRunningState(const httplib::Request& req, httplib::Response& res);
		void httpConfigGetOPCServerState(const httplib::Request& req, httplib::Response& res);
		void httpConfigSetOPCServerState(const httplib::Request& req, httplib::Response& res);
		void httpConfigSetPointValue(const httplib::Request& req, httplib::Response& res);
		void httpConfigGetIEMSConfig(const httplib::Request& req, httplib::Response& res);
		void httpConfigGetIEMSDeviceLogs(const httplib::Request& req, httplib::Response& res);

	private: // local data cache
		bool enableLocalDataCaching_;
		int64_t localDataCachingCount_;
		CLocalDataCaching ldc_;

	private: // cloud
		bool getTimeDifference(int64_t& td);
		bool getCloudTime(int64_t& ct);
		bool downloadVersionList(const std::string& url, const std::string& clientId);
		bool loadVersionList();
		bool downloadConfig(const std::string& url, const std::string& gatewayId);
		bool loadConfigOld(SObjectGateway& sog, std::map<std::string, DRIVER::SChannelConfig>& sccs);
		bool loadConfig(SObjectGateway& sog, std::map<std::string, DRIVER::SChannelConfig>& sccs);
		std::string control(SCloudMessageSptr cloudMessageSptr);
		std::string controlSet(SCloudMessageSptr cloudMessageSptr);
		bool convertSinglePointControl(Json::Value& root, DRIVER::SCtrlInfo& sci);
		bool convertBatchPointControl(Json::Value& root, std::vector<DRIVER::SCtrlInfo>& scis);
		int getCloudFirmwareInfo(std::string &version);
		int getRunFirmwareInfo(std::string& version);
		bool getSerialNumber(std::string &serial);

	private://http server
		bool blank_;
		bool reload_;
		int httpServerPort_;
		httplib::Server httpServer_;
		std::thread* httpServerThread_;
		//get
		void httplinkLive(const httplib::Request& req, httplib::Response& res);
		void httpdeviceInfo(const httplib::Request& req, httplib::Response& res);
		void httppointInfo(const httplib::Request& req, httplib::Response& res);
		void httpdllinfo(const httplib::Request& req, httplib::Response& res);
		void httpremovedll(const httplib::Request& req, httplib::Response& res);
		void httpgetClientinfo(const httplib::Request& req, httplib::Response& res);
		void httpreload(const httplib::Request& req, httplib::Response& res);
		void httppcapinfo(const httplib::Request& req, httplib::Response& res);
		void httpgetModbusMappingInfo(const httplib::Request& req, httplib::Response& res);
		void httpgetUserInfo(const httplib::Request& req, httplib::Response& res);
		void httpgetBasicInfo(const httplib::Request& req, httplib::Response& res);
		void httpgetCloudConfigInfo(const httplib::Request& req, httplib::Response& res);
		void httpgetRuntimeInfo(const httplib::Request& req, httplib::Response& res);
		void httpgetCloudFirmwareInfo(const httplib::Request& req, httplib::Response& res);
		void httpgetInfoSubmission(const httplib::Request& req, httplib::Response& res);
		void httpgetOtherConfigInfo(const httplib::Request& req, httplib::Response& res);
		void httplinkLogList(const httplib::Request& req, httplib::Response& res);
		void httplinkLog(const httplib::Request& req, httplib::Response& res);
		void httplinkInfo(const httplib::Request& req, httplib::Response& res);
		void httplinkEnable(const httplib::Request& req, httplib::Response& res);
		void httprestart(const httplib::Request& req, httplib::Response& res);
		void httpgetNetWorkConfigList(const httplib::Request& req, httplib::Response& res);
		void httpgetWirelessList(const httplib::Request& req, httplib::Response& res);
		void httpsetWirelessDisconnect(const httplib::Request& req, httplib::Response& res);
		void httpUpdate(const httplib::Request& req, httplib::Response& res);
		void httpgetTimeInfo(const httplib::Request& req, httplib::Response& res);
		void httpgetVersionInfo(const httplib::Request& req, httplib::Response& res);
		void httpgetLinkEnableInfo(const httplib::Request& req, httplib::Response& res);
		void httpgetCloudNameInfo(const httplib::Request& req, httplib::Response& res);
		void httpgetCloudStatus(const httplib::Request& req, httplib::Response& res);
		
		//post
		void httpsetPointValue(const httplib::Request& req, httplib::Response& res);
		void httpsetClientInfo(const httplib::Request& req, httplib::Response& res, bool newReturn = false);
		void httpsetModbusMappingInfo(const httplib::Request& req, httplib::Response& res);
		void httpSignUp(const httplib::Request& req, httplib::Response& res);
		void httpSignIn(const httplib::Request& req, httplib::Response& res);
		void httpsetInfoSubmission(const httplib::Request& req, httplib::Response& res);
		void httpsetCloudConfigInfo(const httplib::Request& req, httplib::Response& res);
		void httpsetPassWord(const httplib::Request& req, httplib::Response& res);
		void httpsetOtherConfigInfo(const httplib::Request& req, httplib::Response& res);
		void httplogQuery(const httplib::Request& req, httplib::Response& res);
		void httpsetNetWorkConfig(const httplib::Request& req, httplib::Response& res);
		void httpsetWirelessConnect(const httplib::Request& req, httplib::Response& res);
		void httpsetWirelessConfig(const httplib::Request& req, httplib::Response& res);
		void httplinkLogPackage(const httplib::Request& req, httplib::Response& res);
		void httpsetSyncTime(const httplib::Request& req, httplib::Response& res);
		void httpdoSyncTime(const httplib::Request& req, httplib::Response& res);
		void httpgetEnv(const httplib::Request& req, httplib::Response& res);
		void httpgetCollectType(const httplib::Request& req, httplib::Response& res);

		//put
		void httpupload(const httplib::Request& req, httplib::Response& res);
		void httpuploadDLL(const httplib::Request& req, httplib::Response& res);
		void httpuploadCrt(const httplib::Request& req, httplib::Response& res);

		void httpresResult(httplib::Response& res, const std::string& resultCode, const std::string& resultError);

	public:
		CGatewayAIOT();
		virtual ~CGatewayAIOT();

		//Gateway
		virtual bool initial(GatewayConfig& cfg) override;
		virtual bool uninitial(void) override;
		virtual bool start() override;
		virtual bool stop() override;

		virtual void onEngineStatus(const std::string& channelName, const DRIVER::SStatus& status) override;
		virtual void onEngineOnline(const std::string& channelName, bool online) override;
		virtual void onEngineData(const std::string& channelName, DRIVER::ChannelDataSetSptr dataSetSptr) override;
		virtual void onEngineHistoryData(const std::string& channelName, DRIVER::ChannelHistoryDataSetSptr dataSetSptr) override;
		virtual void onEngineEvent(const std::string& channelName, const std::string& deviceName, SEventSptr eventSptr) override;
		virtual void onEngineControlFeedback(const std::string& channelName, const SControlFeedback& feedback) override;
		virtual void onEngineUpdateDeviceOffline(const std::string& channelName, bool isOffline) override;
		virtual void onEngineUpdateDeviceScanoff(const std::string& channelName, std::string& deviceName, bool scanoff) override;
		virtual void onEngineUpdateDeviceInhibit(const std::string& channelName, std::string& deviceName, bool inhibit) override;

		virtual void onEngineEnable(const std::string& channelName, bool enable);
		virtual SMqttResource onEngineMqttEnable(DRIVER::SCtrlInfo sci, std::map<std::string, DRIVER::SChannelConfig>::iterator slc);
		virtual bool onEngineMqttControl(DRIVER::SCtrlInfo sci);
	};

	REGISTER_GATEWAY(CGatewayAIOT, "GatewayAIOT")

	class CMqttClient : public COMM::ICommMqttCallback
	{
	private:
		CGatewayAIOT& gatewayAIOT_;
		bool isMain_;
		COMM::ICommMqtt* mqttClient_;
		bool mqttConnectionStatus_;
		std::string clientId_;
		std::string brokerUri_; //ip:port
		std::string userName_;
		std::string userPwd_;
		std::string sslCert_;
		std::string sslKey_;
		std::string sslCaCert_;
		bool isPrivate_; //运维平台 

	public:
		explicit CMqttClient(CGatewayAIOT& gatewayAIOT, bool isMain = false, bool isPrivate = false) : gatewayAIOT_(gatewayAIOT), isMain_(isMain), mqttClient_(nullptr), mqttConnectionStatus_(false), isPrivate_(isPrivate) {
			mqttClient_ = static_cast<COMM::ICommMqtt*>(COMM::CFactory::getInstance().produce("CommMqtt"));
		};
		virtual ~CMqttClient() { delete mqttClient_; }

		void configure(const std::string& clientId, const std::string& brokerUri = "127.0.0.1:1883", const std::string& userName = "", const std::string& userPwd = "", const std::string& sslCaCert = "", const std::string& sslCert = "", const std::string& sslKey = "") {
			clientId_ = clientId;
			brokerUri_ = brokerUri;
			userName_ = userName;
			userPwd_ = userPwd;
			sslCert_ = sslCert;
			sslKey_ = sslKey;
			sslCaCert_ = sslCaCert;
			mqttClient_->setCallback(this);
			mqttClient_->configure(clientId_, brokerUri_, userName_, userPwd_, sslCaCert_, sslCert_, sslKey_);

		}
		int connect(int timeoutMs) { return mqttClient_->connect(timeoutMs); }
		int disconnect() { return mqttClient_->disconnect(); }
		int publish(const std::string& topic, const std::string& payload, int qos = 0, int retained = 0) { return mqttClient_->publish(topic, payload, qos, retained); }
		int subscribe(const std::string& topic) { return mqttClient_->subscribe(topic); }
		int subscribe(const std::vector<std::string>& topics) { return mqttClient_->subscribe(topics); }
		int unsubscribe(const std::string& topic) { return mqttClient_->unsubscribe(topic); }
		int unsubscribe(const std::vector<std::string>& topics) { return mqttClient_->unsubscribe(topics); }
		bool getMqttConnectionStatus() const { return mqttConnectionStatus_; }
		std::string getBrokerUri() const { return brokerUri_; }

		//ICommMqttCallback
		virtual void connected() override {
			mqttConnectionStatus_ = true;
			gatewayAIOT_.sog_.cloudConnectionStatus = true;
			gatewayAIOT_.logger_->info("mqtt client connected; uri=" + brokerUri_);
			if (isPrivate_) return;
			gatewayAIOT_.insertLog(eServiceStatus, "云端连接成功; uri=" + brokerUri_);
			if (isMain_)
			{
				mqttClient_->subscribe(clientId_ + "/" + TOPIC_TYPE_CONFIG);
			}

			std::vector<std::string> topics;
			for (auto deviceIdIter : gatewayAIOT_.deviceIdVec_)
			{
				topics.emplace_back(deviceIdIter + "/" + TOPIC_TYPE_CONTROL);
			}
			mqttClient_->subscribe(topics);

			topics.clear();
			for (auto ChannelIdIter : gatewayAIOT_.ChannelIdVec_) 
			{
				topics.emplace_back(ChannelIdIter + "/" + TOPIC_TYPE_CONTROL);
				topics.emplace_back(ChannelIdIter + "/" + TOPIC_TYPE_CONTROLSET);
			}
			mqttClient_->subscribe(topics);
			mqttClient_->subscribe(TOPIC_TYPE_DATA_CALL_ALL);
			mqttClient_->subscribe(TOPIC_TYPE_DATA_CALL_SGL+ gatewayAIOT_.sog_.id);

			std::vector<std::string> channelNames;
			gatewayAIOT_.driverEngine_->getChannelNames(channelNames);
			for (const auto& channelName : channelNames)
			{
				DRIVER::ChannelDataSet dataSet;
				gatewayAIOT_.driverEngine_->getBufferedData(channelName, dataSet);
				SDataSet aiotDataSet;
				aiotDataSet.channelName = channelName;
				aiotDataSet.dataSetSptr = std::make_shared<DRIVER::ChannelDataSet>(dataSet);
				std::unique_lock<std::mutex> lock(gatewayAIOT_.dataSetQueueMutex_);
				gatewayAIOT_.dataSetQueue_.emplace(std::move(aiotDataSet));
				gatewayAIOT_.dataSetQueueCondVar_.notify_one();
			}

			//update device channel _online
			for (const auto& channelName : channelNames)
			{
				DRIVER::ChannelDataSet dataSet;
				gatewayAIOT_.driverEngine_->getDeviceOnlineStatus(channelName, dataSet);
				gatewayAIOT_.driverEngine_->getChannelOnline(channelName, dataSet);
				SDataSet aiotDataSet;
				aiotDataSet.channelName = channelName;
				aiotDataSet.dataSetSptr = std::make_shared<DRIVER::ChannelDataSet>(dataSet);
				std::unique_lock<std::mutex> lock(gatewayAIOT_.dataSetQueueMutex_);
				gatewayAIOT_.dataSetQueue_.emplace(std::move(aiotDataSet));
				gatewayAIOT_.dataSetQueueCondVar_.notify_one();
			}

			// update channel _disable
			for (const auto& slc : gatewayAIOT_.slcs_)
			{
				gatewayAIOT_.onEngineEnable(slc.first, !slc.second.enableStatus);
			}

			gatewayAIOT_.sendAllData();
		}

		virtual void disconnected() override {
			mqttConnectionStatus_ = false;
			gatewayAIOT_.sog_.cloudConnectionStatus = false;
			gatewayAIOT_.logger_->info("mqtt client disconnected; uri=" + brokerUri_);
			if (isPrivate_) return;
			gatewayAIOT_.insertLog(eServiceStatus, "云端连接断开成功; uri=" + brokerUri_);
		}

		virtual void connectFailed() override
		{
			mqttConnectionStatus_ = false;
			gatewayAIOT_.sog_.cloudConnectionStatus = false;
			gatewayAIOT_.logger_->info("mqtt client connectFailed; uri=" + brokerUri_);
			if (isPrivate_) return;
			gatewayAIOT_.insertLog(eServiceStatus, "云端连接失败; uri=" + brokerUri_);
		}

		virtual void connectionLost() override
		{
			mqttConnectionStatus_ = false;
			gatewayAIOT_.sog_.cloudConnectionStatus = false;
			gatewayAIOT_.logger_->info("mqtt client connectionLost; uri=" + brokerUri_);
			if (isPrivate_) return;
			gatewayAIOT_.insertLog(eServiceStatus, "云端连接断开; uri=" + brokerUri_);
		}

		virtual void messageArrived(const std::string& topic, std::string&& payload) override
		{
			SCloudMessage scm;
			scm.cloudID = brokerUri_;
			scm.topic = topic;
			scm.payload = payload;

			std::unique_lock<std::mutex> lock(gatewayAIOT_.cloudMessageQueueMutex_);
			gatewayAIOT_.cloudMessageQueue_.emplace(std::make_shared<SCloudMessage>(scm));
			gatewayAIOT_.mainCondVar_.notify_one();
		}
	};

} //namespace GATEWAY end


#endif
<template>
	<div class="gateway card">
		<div class="gateway-box">
			<el-form ref="gatewayFormRef" :model="gatewayForm" label-width="120px">
				<!-- 修改为组态设置卡片 -->
				<el-card class="box-card" shadow="hover" v-if="basicInfo?.env !== 'iEMS-COM4'">
					<template #header>
						<div class="card-header">
							<span>组态设置</span>
							<div class="btns">
								<el-button type="primary" :loading="saveCollectLoading" @click="saveCollect">保存</el-button>
								<el-button @click="resetCollect">重置</el-button>
							</div>
						</div>
					</template>
					<!-- 添加组态来源下拉选择框 -->
					<el-form-item label="组态来源">
						<el-select v-model="gatewayForm.configSource" placeholder="请选择组态来源" style="width: 214px;">
							<el-option label="物联智控平台组态" value="cloud" />
							<el-option label="本地配置组态" value="local" />
						</el-select>
					</el-form-item>
				</el-card>
				
				<el-card class="box-card" shadow="hover" v-if="basicInfo?.env !== 'iEMS-COM4'">
					<template #header>
						<div class="card-header">
							<span>网关信息</span>
							<div class="btns">
								<el-button type="primary" :loading="saveCloudLoading" @click="saveCloud"  :disabled="basicInfo?.env === 'iEMS-COM4' && gatewayForm.configSource === 'local'">保存</el-button>
								<el-button @click="resetCloud" :disabled="basicInfo?.env === 'iEMS-COM4' && gatewayForm.configSource === 'local'">重置</el-button>
							</div>
						</div>
					</template>
					<el-form-item label="云端地址" prop="mainAddr">
						<div class="item-status">
							<el-input style="width: 214px;" readonly
								:model-value="(handleAddr(gatewayForm.ips) as string)"></el-input>
							<div class="item-btn">
								<el-button :icon="Edit" @click.prevent="editIp(gatewayForm.ips, -1)" :disabled="basicInfo?.env === 'iEMS-COM4' && gatewayForm.configSource === 'local'"></el-button>
							</div>
							<span class="status yes" v-if="gatewayForm.ips.connectStatus">
								<el-icon>
									<SuccessFilled />
								</el-icon>
								<span>云端地址连通</span>
							</span>
							<span class="status no" v-else>
								<el-icon>
									<CircleCloseFilled />
								</el-icon>
								<span>云端地址不通</span>
							</span>
						</div>
					</el-form-item>
					<el-form-item v-for="(item, inx) in gatewayForm.ips.address" :key="'ip' + inx"
						:label="'转发地址' + (inx + 1)" :prop="'ips.' + inx + '.address'">
						<div class="item-status">
							<el-input style="width: 214px;" readonly
								:model-value="(handleAddr(item) as string)"></el-input>
							<div class="item-btn">
								<el-button :icon="Edit" @click.prevent="editIp(item, inx)"></el-button>
								<el-button :icon="Delete" @click.prevent="removeIp(inx)"></el-button>
							</div>
							<span class="status yes" v-if="item.connectStatus">
								<el-icon>
									<SuccessFilled />
								</el-icon>
								<span>转发地址连通</span>
							</span>
							<span class="status no" v-else>
								<el-icon>
									<CircleCloseFilled />
								</el-icon>
								<span>转发地址不通</span>
							</span>
						</div>
					</el-form-item>
					<div class="other-ip">
						<el-button type="primary" @click="addIp" :disabled="basicInfo?.env === 'iEMS-COM4' && gatewayForm.configSource === 'local'">添加转发</el-button>
					</div>
				</el-card>
				<el-card class="box-card" shadow="hover" v-if="basicInfo?.env !== 'iEMS-COM4'">
					<template #header>
						<div class="card-header">
							<span>上送开关</span>
							<el-switch class="send-switch" v-model="gatewayForm.isReport" />
							<div class="btns">
								<el-button type="primary" :loading="saveSubLoading"
									@click="saveSubmission">保存</el-button>
								<el-button @click="resetSubmission">重置</el-button>
							</div>
						</div>
					</template>
					<el-form-item label="信息选择">
						<el-checkbox-group v-model="gatewayForm.infoList" :disabled="!gatewayForm.isReport">
							<el-checkbox disabled label="basicInfo" name="type">基本信息</el-checkbox>
							<el-checkbox label="runtimeInfo" name="type">运行信息</el-checkbox>
						</el-checkbox-group>
					</el-form-item>
					<el-form-item label="上送频率">
						<el-select v-model="gatewayForm.reportInterval" :disabled="!gatewayForm.isReport"
							placeholder="请选择上送频率">
							<el-option v-for="(item) in options" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
				</el-card>
				<el-card class="box-card" style="margin-bottom: 20px" shadow="hover" v-if="basicInfo?.env !== 'iEMS-COM4'">
					<template #header>
						<div class="card-header">
							<span>其它设置</span>
							<div class="btns">
								<el-button type="primary" :loading="saveOtherLoading" @click="saveOther">保存</el-button>
								<el-button @click="resetOther">重置</el-button>
							</div>
						</div>
					</template>
					<el-form-item label="断线缓存">
						<el-switch v-model="gatewayForm.isCache" />
						<div class="warn-info">
							<el-icon>
								<WarnTriangleFilled />
							</el-icon>
							开启后将设置断线缓存
						</div>
					</el-form-item>
					<el-form-item label="云端控制">
						<el-switch v-model="gatewayForm.isControl" />
						<div class="warn-info">
							<el-icon>
								<WarnTriangleFilled />
							</el-icon>
							关闭后将屏蔽云端控制
						</div>
					</el-form-item>
				</el-card>
			</el-form>
			<el-card class="box-card" shadow="hover" v-if="timeForm.isShow">
				<template #header>
					<div class="card-header">
						<span>时间同步</span>
						<div class="btns">
							<el-button type="primary" :loading="saveTimeLoading" @click="saveTime">保存</el-button>
							<el-button @click="resetTime">重置</el-button>
						</div>
					</div>
				</template>
				<el-form ref="timeFormRef" :model="timeForm" :rules="timeRuleForm" label-width="120px">
					<el-form-item label="同步地址" prop="syncAddress">
						<el-input style="width: 214px;margin-right: 20px;" v-model="timeForm.syncAddress"> </el-input>
						<el-button type="primary" plain @click="syncClick">手动同步</el-button>
						<el-checkbox v-model="timeForm.syncSwitch" style="margin-left: 20px;" label="自动同步" />
					</el-form-item>
				</el-form>
			</el-card>
			<el-card class="box-card" shadow="hover" v-if="basicInfo?.env !== 'iEMS-COM4'">
				<template #header>
					<div class="card-header">
						<span>证书上传</span>
						<div class="btns">
							<el-button type="primary" @click="onConfirm">上传</el-button>
							<el-button @click="clearFile">清空</el-button>
						</div>
					</div>
				</template>
				<el-upload ref="uploadRef" class="upload-logo" action="" drag multiple :limit="3" accept=".crt,.key"
					:auto-upload="false" :on-change="handleZipChange" :on-remove="handleRemove" :file-list="fileList">
					<div class="drag-upload">
						<el-icon>
							<UploadFilled />
						</el-icon>
						<div class="el-upload__text">将证书文件拖到此处，或<em>点击上传</em></div>
					</div>
				</el-upload>
			</el-card>
			<el-card class="box-card" shadow="hover">
				<template #header>
					<div class="card-header">
						<span>密码修改</span>
						<div class="btns">
							<el-button type="primary" @click="submitForm(ruleFormRef)">保存</el-button>
							<el-button @click="resetForm(ruleFormRef)">清空</el-button>
						</div>
					</div>
				</template>
				<el-form ref="ruleFormRef" :model="ruleForm" status-icon :rules="passRules" label-width="120px">
					<el-form-item label="原密码" prop="pass">
						<el-input v-model="ruleForm.pass" style="width: 214px" type="password" autocomplete="off" />
					</el-form-item>
					<el-form-item label="新密码" prop="checkPass">
						<el-input v-model="ruleForm.checkPass" style="width: 214px" type="password"
							autocomplete="off" />
					</el-form-item>
					<el-form-item label="再次确认" prop="againPass">
						<el-input v-model.number="ruleForm.againPass" style="width: 214px" type="password"
							autocomplete="off" />
					</el-form-item>
				</el-form>
			</el-card>
			<div class="foot-btn">
				<el-button type="warning" @click="reStart">重启网关</el-button>
			</div>
		</div>
		<CloudConfig ref="cloudConfigRef" @changeConfig="changeConfig"></CloudConfig>
	</div>
</template>

<script setup lang="ts" name="gateway">
import { ref, reactive, onMounted } from "vue";
import { Delete, Edit, UploadFilled, SuccessFilled, CircleCloseFilled, WarnTriangleFilled } from "@element-plus/icons-vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage, ElMessageBox, type UploadFile, type UploadInstance, type UploadFiles, type UploadUserFile } from "element-plus";

import { GatewayBase, GatewaySetting } from "@/api/interface"
import { getCloudInfo, getOtherConfigInfo } from "@/api/modules/basic";
import { changePassword } from "@/api/modules/login";
import { getBasicInfo, getLocalTimeInfo } from "@/api/modules/basic";
import { 
  getInfoSubmission, 
  setInfoSubmission, 
  setCloudConfigInfo, 
  setOtherConfigInfo, 
  restartGateway, 
  uploadCertificate, 
  setSyncTime, 
  doSyncTime,
  getConfigurationSource,
  setConfigurationSource 
} from "@/api/modules/gateway";
import { handleMqtt, isObjectValueEqual } from "@/utils/util";
import { showFullScreenLoading, tryHideFullScreenLoading } from "@/config/serviceLoading";

import CloudConfig from "./cloudDialog.vue";

const gatewayFormRef = ref<FormInstance>();
const ruleFormRef = ref<FormInstance>();
const timeFormRef = ref<FormInstance>();


interface GatewayForm {
	ips: GatewayBase.GatewayCloudInfo;
	isReport: boolean;
	infoList: string[];
	reportInterval: number;
	isCache: boolean;
	isControl: boolean;
	configSource: string;  // 添加组态来源属性
}

interface DialogExpose {
	openDialog: (type: "新增" | "编辑", index: number, config?: GatewayBase.CloudConfig) => void;
}

const options = [{ label: "5分钟", value: 300 }, { label: "30分钟", value: 1800 }, { label: "1小时", value: 3600 }, { label: "4小时", value: 14400 }, { label: "12小时", value: 43200 }, { label: "24小时", value: 86400 }]

const gatewayForm = reactive<GatewayForm>({
	ips: {
		clientID: "",
		username: "",
		password: "",
		resoureIP: "",
		mqttURL: "ssl://",
		connectStatus: false,               //连接状态
		address: [],
		cloudTime: ""
	},
	isReport: true,
	infoList: ["basicInfo"],
	reportInterval: 300,
	isCache: false,
	isControl: false,
	configSource: "cloud"  // 默认选择物联智控平台组态
});

const gatewayFormDefault = ref<GatewayForm>();
const basicInfo = ref<GatewayBase.GatewayBaseInfo>();
const saveCloudLoading = ref(false);
const saveSubLoading = ref(false);
const saveOtherLoading = ref(false);
const saveTimeLoading = ref(false);
const saveCollectLoading = ref(false);

const cloudConfigRef = ref<null | DialogExpose>(null)

const timeForm = reactive({
	isShow: true,
	syncSwitch: true,                   //是否开启时间同步
	syncAddress: "ntp.aliyun.com",                    //时间同步地址
})
const timeRuleForm = reactive<FormRules>({
	syncAddress: [{ required: true, message: '请输入地址', trigger: 'blur' },
	{
		validator(rule: any, value: any, callback: any) {
			const IPPattern = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
			const domainPattern = /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;
			let IPRes = IPPattern.test(value),
				domainRes = domainPattern.test(value);
			if (IPRes || domainRes) {
				callback();
			} else {
				callback(new Error("请输入正确的域名或IP地址"));
			}
		}, trigger: 'blur'
	}]
})

const uploadRef = ref<UploadInstance>();
const fileList = ref<UploadUserFile[]>([]);

const handleZipChange = (file: UploadFile) => {
	fileList.value.push(file);
}
const handleRemove = (_: UploadFile, uploadFiles: UploadFiles) => {
	fileList.value = uploadFiles;
}
const getAccept = () => {
	let result = [];
	for (let i = 0; i < fileList.value.length; i++) {
		let fileName = fileList.value[i].name;
		let accetName = fileName.split('.').pop()!.toLowerCase();
		result.push(accetName);
	}
	return result;
}
const onConfirm = async () => {
	let acceptList = getAccept();
	if (fileList.value.length === 3 && acceptList.includes("crt") && acceptList.includes("key")) {
		try {
			for (let i = 0; i < fileList.value.length; i++) {
				let file = fileList.value[i].raw;
				const formData = new FormData();
				formData.set('file', file!);
				await uploadCertificate(formData);
			}
			ElMessage.success("上传成功");
			clearFile();
		} catch (error) {
			ElMessage.error("上传异常");
			console.log(error);
		}
	} else {
		ElMessage.warning("请上传正确的文件");
	}
}
const clearFile = () => {
	fileList.value = [];
	uploadRef.value && uploadRef.value.clearFiles();
}

const handleAddr = (item: GatewayBase.GatewayCloudInfo | GatewayBase.CloudConfig | undefined): string => {
	if (!item) {
		return "";
	}
	let result = "",
		mqttAddrObj = handleMqtt(item.mqttURL);
	result = mqttAddrObj.ip!;
	return result;
}

const editIp = (item: GatewayBase.GatewayCloudInfo | GatewayBase.CloudConfig, index: number) => {
	cloudConfigRef.value?.openDialog("编辑", index, item)
}

const removeIp = (index: number) => {
	gatewayForm.ips.address.splice(index, 1);

};

const addIp = () => {
	cloudConfigRef.value?.openDialog("新增", gatewayForm.ips.address.length)
};

const changeConfig = ({ key, value }: any) => {
	if (key > -1) {
		if (key === gatewayForm.ips.address.length) {
			gatewayForm.ips.address.splice(key, 1, value);
		} else {
			Object.assign(gatewayForm.ips.address[key], value);
		}

	} else {
		Object.assign(gatewayForm.ips, value);
	}
}

const checkAgainPass = (rule: any, value: any, callback: any) => {
	if (value === "") {
		callback(new Error("请在此输入新密码"));
	} else if (value + "" !== ruleForm.checkPass) {
		callback(new Error("两次输入密码不一致!"));
	} else {
		callback();
	}
};

const validatePass = (rule: any, value: any, callback: any) => {
	if (value === "") {
		callback(new Error("请输入原密码"));
	} else {
		callback();
	}
};
const validatePass2 = (rule: any, value: any, callback: any) => {
	if (value === "") {
		callback(new Error("请输入新密码"));
	} else {
		if (ruleForm.againPass !== "") {
			if (!ruleFormRef.value) return;
			ruleFormRef.value.validateField("againPass", () => null);
		}
		callback();
	}
};

const ruleForm = reactive({
	pass: "",
	checkPass: "",
	againPass: ""
});

const passRules = reactive({
	pass: [{ validator: validatePass, trigger: "blur" }],
	checkPass: [{ validator: validatePass2, trigger: "blur" }],
	againPass: [{ validator: checkAgainPass, trigger: "blur" }]
});

const submitForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	formEl.validate(async (valid: any) => {
		if (valid) {
			try {
				let res = await changePassword({ oldPassWord: ruleForm.pass, newPassWord: ruleForm.checkPass });
				if (res.result && res.result.resultCode == "0") {
					ElMessage.success("修改密码成功！");
					resetForm(ruleFormRef.value)
				} else {
					ElMessage.error(res.result.resultError);
				}
			} catch (error) {
				console.warn(error);
			}
		} else {
			console.log("error submit!");
			return false;
		}
	});
};

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	formEl.resetFields();
};

const saveCloud = async () => {
	if (isObjectValueEqual(gatewayForm, gatewayFormDefault.value!)) {
		ElMessage.warning("没有修改项！");
		return;
	}
	//网关云端配置信息
	let cloudParams: GatewaySetting.ReqCloudConfigParams = {
		clientID: "",
		username: "",
		password: "",
		resoureIP: "",
		mqttURL: "ssl://",
		address: []
	};
	({
		clientID: cloudParams.clientID,
		username: cloudParams.username,
		password: cloudParams.password,
		resoureIP: cloudParams.resoureIP,
		mqttURL: cloudParams.mqttURL
	} = { ...gatewayForm.ips });
	cloudParams.address = gatewayForm.ips.address.map(v => ({
		clientID: v.clientID,
		username: v.username,
		password: v.password,
		resoureIP: v.resoureIP,
		mqttURL: v.mqttURL,
	}));
	/* 平台地址和数据地址的唯一性校验 */
	let IPList = [cloudParams.resoureIP].concat(cloudParams.address.map(v => v.resoureIP)),
		IPMap = new Map();
	let mqttList = [cloudParams.mqttURL].concat(cloudParams.address.map(v => v.mqttURL)),
		mqttMap = new Map();
	for (let i = 0; i < IPList.length; i++) {
		if (IPMap.has(IPList[i])) {
			ElMessage.error(`平台地址存在重复:${IPList[i]}`);
			return;
		} else {
			IPMap.set(IPList[i], 1);
		}
	}
	for (let i = 0; i < mqttList.length; i++) {
		if (mqttMap.has(mqttList[i])) {
			ElMessage.error(`数据地址存在重复:${mqttList[i].replace("ssl://", "")}`);
			return;
		} else {
			mqttMap.set(mqttList[i], 1);
		}
	}
	/* end */
	try {
		saveCloudLoading.value = true;
		await setCloudConfigInfo(cloudParams);
		saveCloudLoading.value = false;
		ElMessage.success("网关信息保存成功");
		setTimeout(() => {
			initData();
		}, 1000)
	} catch (error) {
		console.log(error)
		saveCloudLoading.value = false;
	}
}
const resetCloud = () => {
	gatewayForm.ips = JSON.parse(JSON.stringify(gatewayFormDefault.value!.ips));
}

const saveSubmission = async () => {
	if (isObjectValueEqual(gatewayForm, gatewayFormDefault.value!)) {
		ElMessage.warning("没有修改项！");
		return;
	}
	saveSubLoading.value = true;
	try {
		//网关上送配置信息
		let subParams: GatewaySetting.ReqSubmissionParams = {
			isReport: gatewayForm.isReport,
			basicInfo: gatewayForm.infoList.includes("basicInfo"),
			runtimeInfo: gatewayForm.infoList.includes("runtimeInfo"),
			reportInterval: gatewayForm.reportInterval
		};
		await setInfoSubmission(subParams);

		saveSubLoading.value = false;
		ElMessage.success("上送配置保存成功");
		initData();
	} catch (error) {
		console.log(error)
		saveSubLoading.value = false;
	}
}
const resetSubmission = () => {
	gatewayForm.isReport = gatewayFormDefault.value!.isReport;
	gatewayForm.reportInterval = gatewayFormDefault.value!.reportInterval;
	gatewayForm.infoList = JSON.parse(JSON.stringify(gatewayFormDefault.value!.infoList));
}

const saveOther = async () => {
	if (isObjectValueEqual(gatewayForm, gatewayFormDefault.value!)) {
		ElMessage.warning("没有修改项！");
		return;
	}
	if (!gatewayForm.isControl) {
		try {
			await ElMessageBox.confirm(
				'云端控制是否关闭?',
				'请注意',
				{
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning',
				}
			)
		} catch (error) {
			gatewayForm.isControl = true;
		}
	}
	saveOtherLoading.value = true;
	try {
		//网关其他配置信息
		let otherParams: GatewaySetting.ReqOtherConfigParams = {
			disConnCache: gatewayForm.isCache,
			cloudControlAuth: gatewayForm.isControl
		}
		await setOtherConfigInfo(otherParams);
		saveOtherLoading.value = false;
		ElMessage.success("保存成功");
		initData();
	} catch (error) {
		console.log(error)
		saveOtherLoading.value = false;
	}
}
const resetOther = () => {
	gatewayForm.isControl = gatewayFormDefault.value!.isControl;
	gatewayForm.isCache = gatewayFormDefault.value!.isCache;
}

const saveTime = async () => {
	timeFormRef.value!.validate(async (valid: any) => {
		if (valid) {
			saveTimeLoading.value = true;
			try {
				//网关时间同步信息
				let timeParams: GatewaySetting.ReqTimeParams = {
					syncSwitch: timeForm.syncSwitch,
					syncAddress: timeForm.syncAddress
				}
				await setSyncTime(timeParams);
				saveTimeLoading.value = false;
				ElMessage.success("保存成功");
				initData();
			} catch (error) {
				console.log(error)
				saveTimeLoading.value = false;
			}
		} else {
			console.log("error submit!");
			return false;
		}
	});
}
const resetTime = async () => {
	saveTimeLoading.value = true;
	try {
		let timeInfo = await getLocalTimeInfo();
		saveTimeLoading.value = false;
		if (timeInfo.result && timeInfo.result.resultCode == "0") {
			let data = timeInfo.data;
			timeForm.syncAddress = data.syncAddress;
			timeForm.syncSwitch = data.syncSwitch;
		}
	} catch (error) {
		console.warn("get time error =>", error)
		saveTimeLoading.value = false;
	}
}
const syncClick = async () => {
	timeFormRef.value!.validate(async (valid: any) => {
		if (valid) {
			console.log(valid)
			saveTimeLoading.value = true;
			try {
				//网关时间同步信息
				let timeParams = {
					syncAddress: timeForm.syncAddress
				}
				await doSyncTime(timeParams);
				saveTimeLoading.value = false;
				ElMessage.success("下发成功");
			} catch (error) {
				console.log(error)
				saveTimeLoading.value = false;
			}
		} else {
			console.log("error submit!");
			return false;
		}
	});
}

const reStart = () => {
	try {
		ElMessageBox.confirm(
			'网关将会重启，页面暂时无法响应，确认重启?',
			'请注意',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		)
			.then(async () => {
				let res = await restartGateway();
				if (res.result && res.result.resultCode === "0") {
					ElMessage.success("请等待重启，预计30秒。");
					showFullScreenLoading();
					setTimeout(() => {
						tryHideFullScreenLoading();
						window.location.reload();
					}, 30000);
				}
			})
			.catch(() => {
				console.log("取消重启")
			});
	} catch (error) {
		console.warn(error)
	}

}

const initData = async () => {
	try {
		let res = await getBasicInfo();
		if (res.result && res.result.resultCode == '0') {
			timeForm.isShow = (res.data.env != 'normal');
			basicInfo.value = res.data
		}
	} catch (error) {
		console.warn(error)
	}
	try {
		// 获取组态配置来源
		let configSourceRes = await getConfigurationSource();
		if (configSourceRes.result && configSourceRes.result.resultCode == "0") {
			console.log("组态来源数据:", configSourceRes.data);
			// 根据接口返回设置组态来源
			if (configSourceRes.data && configSourceRes.data.type) {
				gatewayForm.configSource = configSourceRes.data.type;
				
				// 确保gatewayFormDefault已初始化
				if (!gatewayFormDefault.value) {
					gatewayFormDefault.value = JSON.parse(JSON.stringify(gatewayForm));
				} else {
					// 更新默认值中的configSource
					gatewayFormDefault.value.configSource = configSourceRes.data.type;
				}
			}
		}
	} catch (error) {
		console.warn("get configSource error =>", error)
	}
	
	try {
		//获取网关云端地址信息
		let cloudInfo = await getCloudInfo();
		if (cloudInfo.result && cloudInfo.result.resultCode == "0") {
			gatewayForm.ips = cloudInfo.data;
		}
	} catch (error) {
		console.warn("get cloudinfo error =>", error)
	}
	try {
		let subInfo = await getInfoSubmission();
		if (subInfo.result && subInfo.result.resultCode == "0") {
			let data = subInfo.data;
			gatewayForm.isReport = data.isReport;
			gatewayForm.reportInterval = data.reportInterval;
			gatewayForm.infoList = [];
			if (data.basicInfo) {
				gatewayForm.infoList.push("basicInfo");
			}
			if (data.runtimeInfo) {
				gatewayForm.infoList.push("runtimeInfo");
			}
		}
	} catch (error) {
		console.warn("get subInfo error =>", error)
	}
	try {
		let otherInfo = await getOtherConfigInfo();
		if (otherInfo.result && otherInfo.result.resultCode == "0") {
			let data = otherInfo.data;
			gatewayForm.isControl = data.cloudControlAuth;
			gatewayForm.isCache = data.disConnCache;
		}
	} catch (error) {
		console.warn("get otherInfo error =>", error)
	}
	try {
		let timeInfo = await getLocalTimeInfo();
		if (timeInfo.result && timeInfo.result.resultCode == "0") {
			let data = timeInfo.data;
			timeForm.syncAddress = data.syncAddress;
			timeForm.syncSwitch = data.syncSwitch;
		}
	} catch (error) {
		console.warn("get time error =>", error)
	}

	//获取最初的gateway
	gatewayFormDefault.value = JSON.parse(JSON.stringify(gatewayForm));
}
onMounted(() => {
	initData();
})

// 添加保存组态设置的方法
const saveCollect = async () => {
  // 判断组态来源是否被修改
  if (gatewayForm.configSource === gatewayFormDefault.value?.configSource) {
    ElMessage.warning("组态来源未修改！");
    return;
  }
  
  saveCollectLoading.value = true;
  try {
    // 调用设置组态来源的接口
    const params = { type: gatewayForm.configSource };
    const res = await setConfigurationSource(params);
    
    if (res.result && res.result.resultCode === "0") {
      ElMessage.success("保存成功，3秒后请刷新页面");
      
      // 更新默认值
      if (gatewayFormDefault.value) {
        gatewayFormDefault.value.configSource = gatewayForm.configSource;
      }
      
      // 显示全屏加载
      showFullScreenLoading();
      
      // 3秒后刷新页面
      setTimeout(() => {
        tryHideFullScreenLoading();
        window.location.reload();
      }, 3000);
    } else {
      ElMessage.error(`设置失败: ${res.result?.resultError || '未知错误'}`);
    }
  } catch (error) {
    console.error("设置组态来源失败:", error);
    ElMessage.error("设置组态来源失败");
  } finally {
    saveCollectLoading.value = false;
  }
};

// 添加重置组态设置的方法
const resetCollect = () => {
  // 重置组态来源为默认值
  if (gatewayFormDefault.value) {
    gatewayForm.configSource = gatewayFormDefault.value.configSource;
  }
};
</script>

<style scoped lang="scss">
.gateway {
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
	width: 100%;

	.gateway-box {
		width: 750px;
		margin: 0 auto;

		.box-card {
			margin-bottom: 20px;

			// &:last-child {
			// 	margin-bottom: 0;
			// }
		}
	}

	.el-card {
		:deep(.el-card__header) {
			background-color: #f7f7f7;
		}

		.card-header {
			position: relative;
		}

		.btns {
			display: flex;
			justify-content: center;
			position: absolute;
			right: 10px;
			top: -5px;

			button+button {
				margin-left: 20px;
			}
		}

		.send-switch {
			position: absolute;
			left: 84px;
			top: -5px;
		}

		.warn-info {
			margin-left: 20px;
		}

		.other-ip {
			padding-left: 42px;
		}

		.item-status {
			display: inline-flex;
			justify-content: space-between;
			width: 460px;

			.item-btn {
				width: 104px;
			}

			.ip {
				margin-right: 20px;
			}

			.status {
				display: flex;
				line-height: 14px;
				padding-top: 8px;

				span {
					margin-left: 4px;
				}
			}

			.yes {
				color: #67c23a;
			}

			.no {
				color: #f56c6c;
			}
		}
	}

	.warn-info {
		display: flex;
		color: #e6a23c;
		line-height: 14px;
	}

	.foot-btn {
		width: 100%;
		display: flex;
		justify-content: center;
	}
}

.empty-content {
  min-height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
  font-size: 14px;
}
</style>

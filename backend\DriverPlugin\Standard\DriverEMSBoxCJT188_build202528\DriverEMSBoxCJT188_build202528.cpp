#include "DriverEMSBoxCJT188_build202528.h"

#include <iostream>

#include "Utils/EMSBOXTool.h"
#include "Utils/Utils.hpp"

namespace DRIVER
{
	void CDriverEMSBoxCJT188_build202528::initialRequest(uint8_t* request)
	{
		request[0] = 0x68;	// frame start
		request[9] = 0x1;   // read
		request[10] = 0x3;	// data length
		request[11] = 0x1f; // data id
		request[12] = 0x90;	// data id
		request[13] = 0x0;
		request[15] = 0x16; // frame end
	}

	void CDriverEMSBoxCJT188_build202528::setMeterType(uint8_t* request, EMeterType type)
	{
		request[1] = type;
	}

	void CDriverEMSBoxCJT188_build202528::setMeterAddress(uint8_t* request, std::string number)
	{
		uint8_t tmp = 0;
		for (int i = 0; i < 6; ++i)
		{
			if (number.length() > 1)
			{
				tmp = atoi(number.substr(number.length() - 2, 2).c_str());
				number.erase(number.length() - 2, 2);
			}
			else
			{
				tmp = 0;
			}

			request[2 + i] = tmp / 10 * 16 + tmp % 10;
		}
	}

	void CDriverEMSBoxCJT188_build202528::setCheckCode(uint8_t* request)
	{
		uint8_t sum = 0x0;
		for (size_t i = 0; i < 14; ++i)
		{
			sum += request[i];
		}
		request[14] = sum;
	}

	bool CDriverEMSBoxCJT188_build202528::valid(uint8_t* response, int responseLen)
	{
		//check len
		if (responseLen < 13 || responseLen > 0x64)
		{
			return false;
		}

		if (response[10] + 13 != responseLen)
		{
			return false;
		}

		//check tail
		if (response[responseLen - 1] != 0x16)
		{
			return false;
		}

		//check checksum
		uint8_t sum = 0;
		for (size_t i = 0; i < responseLen - 2; ++i)
		{
			sum += response[i];
			sum = sum % 256;
		}

		if (sum != response[responseLen - 2])
		{
			return false;
		}

		// check device number
		for (int i = 2; i < 8; i++)
		{
			if (request_[i] != response[i])
				return false;
		}

		return true;
	}

	void CDriverEMSBoxCJT188_build202528::getMonthUsage(uint8_t* response, float& monthUsage)
	{
		uint32_t dataPosBase = 19;
		monthUsage = response[dataPosBase + 3] / 16 * 10000000;
		monthUsage += response[dataPosBase + 3] % 16 * 1000000;
		monthUsage += response[dataPosBase + 2] / 16 * 100000;
		monthUsage += response[dataPosBase + 2] % 16 * 10000;
		monthUsage += response[dataPosBase + 1] / 16 * 1000;
		monthUsage += response[dataPosBase + 1] % 16 * 100;
		monthUsage += response[dataPosBase + 0] / 16 * 10;
		monthUsage += response[dataPosBase + 0] % 16 * 1;
		monthUsage = monthUsage / 100;
	}

	void CDriverEMSBoxCJT188_build202528::getTotalUsage(uint8_t* response, float& totalUsage)
	{
		uint32_t dataPosBase = 14;
		//cjt188_data_parse_by_format_to_float(response + dataPosBase, 4, "XXXXXX.XX", (uint8_t*)&totalUsage);
		totalUsage = response[dataPosBase + 3] / 16 * 10000000;
		totalUsage += response[dataPosBase + 3] % 16 * 1000000;
		totalUsage += response[dataPosBase + 2] / 16 * 100000;
		totalUsage += response[dataPosBase + 2] % 16 * 10000;
		totalUsage += response[dataPosBase + 1] / 16 * 1000;
		totalUsage += response[dataPosBase + 1] % 16 * 100;
		totalUsage += response[dataPosBase + 0] / 16 * 10;
		totalUsage += response[dataPosBase + 0] % 16 * 1;
		totalUsage = totalUsage / 100;
	}

	size_t CDriverEMSBoxCJT188_build202528::getTime(uint8_t* response)
	{
		return 0;
	}

	bool CDriverEMSBoxCJT188_build202528::readFromDevice(const SPointProtocol* pp, SValue& value)
	{
		initialRequest(request_);
		setMeterType(request_, pp->meterType);
		setMeterAddress(request_, pp->meterAddress);
		setCheckCode(request_);

		serialPortClient_->send({ request_, request_ + 16 }, 100);
		cb_->onLog(ELogLevel::eLogPacket, UTILS::toIEMSPacketMessage(pp->deviceId, "send", bytesToHexString(request_, 16)));

		std::vector<uint8_t> response;
		serialPortClient_->recv(response, 1000);
		if (response.empty())
		{
			cb_->onLog(ELogLevel::eLogPacket, UTILS::toIEMSPacketMessage(pp->deviceId, "recv", "no datas"));
			return false;
		}
		cb_->onLog(ELogLevel::eLogPacket, UTILS::toIEMSPacketMessage(pp->deviceId, "recv", bytesToHexString(response.data(), response.size())));


		//trim
		while (response[0] == 0xfe)
		{
			response.erase(response.begin());
		}
		
		// parse
		if (valid(response.data(), response.size()))
		{
			float data = 0.0;
			if (response[10] == 0x16)
			{
				getTotalUsage(response.data(), data);
			}
			else if (response[10] == 0x9)
			{
				getMonthUsage(response.data(), data);
			}
			value = data;
			return true;
		}

		return false;
	}

	CDriverEMSBoxCJT188_build202528::CDriverEMSBoxCJT188_build202528() : serialPortClient_(nullptr)
	{
		memset(request_, 0, 16);
	}

	CDriverEMSBoxCJT188_build202528::~CDriverEMSBoxCJT188_build202528()
	{

	}

	bool CDriverEMSBoxCJT188_build202528::open(const SProtocolNode& pn)
	{
		SChannelProtocol* channelProtocol = static_cast<SChannelProtocol*>(pn.protocol);

		serialPortClient_ = static_cast<COMM::ICommSerialPort*>( COMM::CFactory::getInstance().produce("CommSerialPort") );
		serialPortClient_->configure(channelProtocol->device, channelProtocol->baud, channelProtocol->dataBit, channelProtocol->stopBit, channelProtocol->parity);
		serialPortClient_->connect(100);
		initialRequest(request_);

		return true;
	}

	bool CDriverEMSBoxCJT188_build202528::close(const SProtocolNode& pn)
	{
		if (serialPortClient_)
		{
			serialPortClient_->disconnect();
			COMM::CFactory::getInstance().destory(serialPortClient_);
			serialPortClient_ = nullptr;
		}
		return true;
	}

	EStatusCode CDriverEMSBoxCJT188_build202528::control(const IProtocol* const channelProtocol, const IProtocol* const deviceProtocol, const IProtocol* const pointProtocol, const SControlInfo& controlInfo)
	{
		return eStatusFail;
	}

	EStatusCode CDriverEMSBoxCJT188_build202528::controlSet(const std::unordered_map<std::string, std::vector<SControlSetInfo>>& controlValues)
	{
		return EStatusCode();
	}

	void CDriverEMSBoxCJT188_build202528::setCallback(IDriverCallback* cb)
	{
		cb_ = cb;
	}

	EStatusCode CDriverEMSBoxCJT188_build202528::poll(const SProtocolNode& pn)
	{
		SData data;
		for (auto& pointProtocol : pn.sub)
		{
			const SPointProtocol* protocol = static_cast<const SPointProtocol*>(pointProtocol.protocol);
			if (readFromDevice(protocol, data.value) == false)
			{
				continue;
			}
			data.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
			if (cb_)
			{
				cb_->onData(protocol->toString(), data);
			}
		}
		return eStatusSuccess;
	}

	std::string CDriverEMSBoxCJT188_build202528::getPointKey(const IProtocol* const channelProtocol, const IProtocol* const deviceProtocol, const IProtocol* const pointProtocol)
	{
		const SPointProtocol* const protocol = static_cast<const SPointProtocol* const>(pointProtocol);
		return protocol->toString();
	}

	IProtocol* CDriverEMSBoxCJT188_build202528::createProtocol(EProtocolType type, OriginProtocol& originProtocol)
	{
		switch (type)
		{
		case EProtocolType::eProtocolLink:
		{
			SChannelProtocol* protocol = new SChannelProtocol;
			protocol->device = originProtocol["DeviceName"];						//The device argument specifies the name of the serial port handled by the OS, eg. "/dev/ttyS0" or "/dev/ttyUSB0". On Windows, it��s necessary to prepend COM name with "\\.\" for COM number greater than 9, eg. "\\\\.\\COM10". See http://msdn.microsoft.com/en-us/library/aa365247(v=vs.85).aspx for details
			protocol->baud = UTILS::str2Number<int>(originProtocol["BaudRate"]);	//The baud argument specifies the baud rate of the communication, eg. 9600, 19200, 57600, 115200, etc.
			protocol->parity = originProtocol["Parity"].empty() ? 'N' : originProtocol["Parity"][0];							//The parity argument can have one of the following values : N for none   E for even  O for odd
			protocol->dataBit = UTILS::str2Number<int>(originProtocol["DataBit"]);	//The data_bits argument specifies the number of bits of data, the allowed values are 5, 6, 7 and 8.
			protocol->stopBit = UTILS::str2Number<int>(originProtocol["StopBit"]);	//The stop_bits argument specifies the bits of stop, the allowed values are 1 and 2.
			return protocol;
		}
		case EProtocolType::eProtocolDevice:
		{
			return nullptr;
		}
		case EProtocolType::eProtocolPoint:
		{
			SPointProtocol* protocol = new SPointProtocol;
			protocol->meterAddress = originProtocol["Address"];
			protocol->meterType = (EMeterType)UTILS::str2Number<int>(originProtocol["MeterType"]);
			protocol->deviceId = originProtocol["_device"];
			//protocol->isTotalUsage = originProtocol["isTotalUsage"] == "true" ? true : false;
			return protocol;
		}
		default:
			break;
		}
		return nullptr;
	}

	void CDriverEMSBoxCJT188_build202528::destoryProtocol(EProtocolType type, IProtocol* protocol)
	{
		if (protocol)
		{
			delete protocol;
		}
	}

	void CDriverEMSBoxCJT188_build202528::setFlag(EFlag flag)
	{

	}

} //namespace DRIVER

extern "C"
{
	DRIVER_API DRIVER::IDriver* createDriver()
	{
		return new DRIVER::CDriverEMSBoxCJT188_build202528;
	}

	DRIVER_API void destoryDriver(DRIVER::IDriver* p)
	{
		if (p) { delete p; }
	}
}
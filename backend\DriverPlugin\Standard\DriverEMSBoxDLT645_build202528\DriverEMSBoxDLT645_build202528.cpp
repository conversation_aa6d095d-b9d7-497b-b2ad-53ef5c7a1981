#include "DriverEMSBoxDLT645_build202528.h"

#include <math.h>

#include <iostream>
#include <chrono>

#include "Utils/Utils.hpp"
#include "Utils/EMSBOXTool.h"

namespace DRIVER
{
	/**
	 * Name:    dlt645_set_addr
	 * Brief:   ���ô�վ��ַ
	 * Input:
	 *  @addr:      ��վ��ַ
	 * Output:  None
	 */
	void CDriverEMSBoxDLT645_build202528::dlt645_set_addr(std::string addr)
	{
		uint8_t tmp = 0;
		for (int i = 0; i < DL645_ADDR_LEN; ++i)
		{
			if (addr.length() > 1)
			{
				tmp = atoi(addr.substr(addr.length() - 2, 2).c_str());
				addr.erase(addr.length() - 2, 2);
			}
			else
			{
				tmp = 0;
			}
			
			addr_[i] = tmp / 10 * 16 + tmp % 10;
		}
	}

	/**
	 * Name:    dec2bcd
	 * Brief:   ʮ����תBCD�루Ŀǰ֧��32λ���ִ�С��
	 * Input:
	 *  @val:   ʮ����ֵ
	 * Output:  BCD��ֵ
	 */
	uint32_t CDriverEMSBoxDLT645_build202528::dec_to_bcd(uint32_t val)
	{
		uint32_t data = 0;

		if (val < 100)
		{
			uint8_t byte0 = val % 10;
			uint8_t byte1 = val / 10;
			data = (byte1 << 4) + byte0;
		}
		else if (val < 10000)
		{
			uint8_t byte0 = val % 10;
			uint8_t byte1 = (val / 10) % 10;
			uint8_t byte2 = (val / 100) % 10;
			uint8_t byte3 = (val / 1000) % 10;
			data = (byte3 << 12) +
				(byte2 << 8) +
				(byte1 << 4) + byte0;
		}
		else if (val < 1000000)
		{
			uint8_t byte0 = val % 10;
			uint8_t byte1 = (val / 10) % 10;
			uint8_t byte2 = (val / 100) % 10;
			uint8_t byte3 = (val / 1000) % 10;
			uint8_t byte4 = (val / 10000) % 10;
			uint8_t byte5 = (val / 100000) % 10;
			data = (byte5 << 20) +
				(byte4 << 16) +
				(byte3 << 12) +
				(byte2 << 8) +
				(byte1 << 4) + byte0;
		}
		else if (val < 100000000)
		{
			uint8_t byte0 = val % 10;
			uint8_t byte1 = (val / 10) % 10;
			uint8_t byte2 = (val / 100) % 10;
			uint8_t byte3 = (val / 1000) % 10;
			uint8_t byte4 = (val / 10000) % 10;
			uint8_t byte5 = (val / 100000) % 10;
			uint8_t byte6 = (val / 1000000) % 10;
			uint8_t byte7 = (val / 10000000) % 10;
			data = (byte7 << 28) +
				(byte6 << 24) +
				(byte5 << 20) +
				(byte4 << 16) +
				(byte3 << 12) +
				(byte2 << 8) +
				(byte1 << 4) + byte0;
		}
		return data;
	}

	/**
	 * Name:    str_to_bcd
	 * Brief:   �ַ���תBCD��ʽ
	 * Input:
	 *  @str:               Ҫת�����ַ���
	 *  @bcd_store_address: ת����Ĵ洢��ַ
	 *  @bcd_len:           BCD���ܳ���
	 * Output:  �ɹ�0��ʧ��-1
	 */
	int CDriverEMSBoxDLT645_build202528::str_to_bcd(char* str, uint8_t* bcd_store_address, uint16_t bcd_len)
	{
		//�ַ���ƫ��
		int str_pos = bcd_len * 2 - strlen(str);
		//�ַ�����BCD�볤�ȳ�
		if (str_pos < 0)
		{
			return -1;
		}
		memset(bcd_store_address, 0, bcd_len);

		for (int i = 0; i < strlen(str); i++)
		{
			if (str[i] >= '0' && str[i] <= '9')
			{
				bcd_store_address[(i + str_pos) / 2] |= (str[i] - '0') << (4 * ((i + 1 + (strlen(str) % 2)) % 2));
			}
			else
			{
				//��ǰ�ַ���Ϊ���֣����ش���
				return -1;
			}
		}
		return 0;
	}

	/**
	 * Name:    dlt645_common_check
	 * Brief:   645Э��������ݹ�������У��
	 * Input:
	 *  @msg:   У�����ݰ�
	 *  @len:   ���ݰ�����
	 *  @addr:  ��վ��ַ
	 * Output:  У��ɹ���0��У��ʧ�ܣ�-1
	 */
	int CDriverEMSBoxDLT645_build202528::dlt645_common_check(uint8_t* msg, int len, uint8_t* addr)
	{
		//���ݰ�����У��
		if (len < 7)
		{
			return -1;
		}
		//����֡��־У��
		if (msg[0] != DL645_START_CODE ||
			msg[DL645_ADDR_LEN + 1] != DL645_START_CODE ||
			msg[len - 1] != DL645_STOP_CODE)
		{
			//m_logCB(ELogLevel::eLogError,"check code error!\n");
			return -1;
		}
		//CRCУ��
		uint8_t crc = _crc(msg, len - 2);
		if (crc != msg[len - 2])
		{
			//m_logCB(ELogLevel::eLogError,"check crc error!\n");
			return -1;
		}
		//����������У��
		if ((msg[DL645_CONTROL_POS] & C_TD_MASK) == (C_TD_MASTER << C_TD_POS))
		{
			//m_logCB(ELogLevel::eLogError,"check control direction error!\n");
			return -1;
		}
		//������Ӧ��У��
		if ((msg[DL645_CONTROL_POS] & C_ACK_MASK) == (C_ACK_ERR << C_ACK_POS))
		{
			//m_logCB(ELogLevel::eLogError,"check ACK error!\n");
			return msg[len - 3];
		}
		//��վ��ַУ��
		if (memcmp(msg + 1, addr, 6) != 0)
		{
			return -1;
		}

		return 0;
	}

	/**
	 * Name:    _crc
	 * Brief:   crc��У��
	 * Input:
	 *  @msg:   У�����ݰ�
	 *  @len:   ���ݰ�����
	 * Output:  У��ֵ
	 */
	int CDriverEMSBoxDLT645_build202528::_crc(uint8_t* msg, int len)
	{
		uint8_t crc = 0;
		while (len--)
		{
			crc += *msg++;
		}
		return crc;
	}

	/**
	 * Name:    data_package_translate_to_int
	 * Brief:   �����յ���dlt645���ݰ��е�����ת��Ϊ����
	 * Input:
	 *  @read_data: �����׵�ַ
	 *  @len:       ���ݳ���
	 * Output:  ת���������
	 */
	int CDriverEMSBoxDLT645_build202528::data_package_translate_to_int(uint8_t* read_data, uint16_t len)
	{
		//Ȩֵ
		uint8_t number_weight = 0;
		//��ǰ�����±�����
		uint8_t current_index = 0;
		//��ǰ�����ֽ�λ
		uint8_t current_byte_part = BYTE_RESET;
		//��ǰ����ֵ
		int i_value = 0;

		while (len--)
		{
			current_byte_part = BYTE_LOW_ADDRESS;
			do
			{
				switch (current_byte_part)
				{
					//��ǰ�����ֽڵ�λ
				case BYTE_LOW_ADDRESS:
					i_value += ((read_data[current_index] - 0x33) & 0x0f) * pow(10, number_weight);
					number_weight++;
					current_byte_part = BYTE_HIGH_ADDRESS;
					break;
					//��ǰ�����ֽڸ�λ
				case BYTE_HIGH_ADDRESS:
					i_value += ((read_data[current_index] - 0x33) >> 4) * pow(10, number_weight);
					number_weight++;
					current_byte_part = BYTE_RESET;
					break;
				}
			} while (current_byte_part != BYTE_RESET);
			current_index++;
		}
		return i_value;
	}

	/**
	 * Name:    dlt645_data_parse_by_format_to_float
	 * Brief:   �������ݸ�ʽ��645Э���ȡ������ת��Ϊ��ʵ���ݲ��洢
	 *          ����ʵ����Ϊ�������ݣ���Ҫע��������۶�ȡ���ݳ����Ƕ��٣��洢���ݳ��ȶ�Ӧ��4�ֽ�
	 * Input:
	 *  @read_data:     645Э���ȡ������
	 *  @read_len:      ��ȡ���ݵĳ���
	 *  @data_format:   ת�������ݸ�ʽ���� XX.XX,XX.XXX
	 * Output:  ת���ɹ�����0��ʧ�ܷ���-1
	 */
	int CDriverEMSBoxDLT645_build202528::dlt645_data_parse_by_format_to_float(uint8_t* read_data, uint16_t read_len, const char* data_format, uint8_t* store_address)
	{
		//Ȩֵ
		int num_weight = 0;
		int ival = data_package_translate_to_int(read_data, read_len);

		for (int i = 0; i < strlen(data_format); i++)
		{
			if (*(data_format + i) == '.')
			{
				num_weight = strlen(data_format) - i - 1;
				if (num_weight < 0)
				{
					return -1;
				}
				break;
			}
		}
		float fval = ival / pow(10, num_weight);
		memcpy(store_address, &fval, 4);
		return 0;
	}

	/**
	 * Name:    dlt645_1997_read_data
	 * Brief:   DLT645-1997 ���ݶ�ȡ
	 * Input:
	 *  @addr:          ��վ��ַ
	 *  @code:          ���ݱ�ʶ
	 *  @read_data:     ���ݴ洢��ַ
	 * Output:  �ɹ��������ݳ��ȣ�ʧ�ܷ���-1
	 */
	int CDriverEMSBoxDLT645_build202528::dlt645_1997_read_data(uint32_t code, uint8_t* read_data, const SPointProtocol* pp)
	{
		uint8_t send_buf[DL645_1997_RD_CMD_LEN];
		uint8_t read_buf[DL645_RESP_LEN];

		memset(read_buf, 0, sizeof(read_buf));
		memset(send_buf, 0, sizeof(send_buf));

		memcpy(send_buf + 1, addr_, DL645_ADDR_LEN);
		send_buf[DL645_CONTROL_POS] = C_1997_CODE_RD;
		send_buf[DL645_LEN_POS] = 2;

		uint8_t send_code[2] = { 0 };
		send_code[0] = (code & 0xff) + 0x33;
		send_code[1] = ((code >> 8) & 0xff) + 0x33;
		memcpy(send_buf + DL645_DATA_POS, send_code, 2);

		if (dlt645_send_msg(send_buf, DL645_1997_RD_CMD_LEN, pp) < 0)
		{
			//m_logCB(ELogLevel::eLogError,"send data error!\n");
			return -1;
		}

		if (dlt645_receive_msg(read_buf, DL645_RESP_LEN, code, 1997, pp) < 0)
		{
			//m_logCB(ELogLevel::eLogError,"receive msg error!\n");
			return -1;
		}

		return dlt645_1997_parsing_data(code, read_buf + DL645_DATA_POS + 2, read_buf[DL645_LEN_POS] - 2, read_data);
	}

	/**
	 * Name:    dlt645_1997_recv_check
	 * Brief:   DLT645-1997 ����У��
	 * Input:
	 *  @msg:   У�����ݰ�
	 *  @len:   ���ݰ�����
	 *  @addr:  ��վ��ַ
	 *  @code:  ������
	 * Output:  У��ɹ���0 ��ʧ�� -1
	 */
	int CDriverEMSBoxDLT645_build202528::dlt645_1997_recv_check(uint8_t* msg, int len, uint8_t* addr, uint32_t code)
	{
		if (dlt645_common_check(msg, len, addr) < 0)
		{
			return -1;
		}
		if (msg[DL645_CONTROL_POS] == 0x84)
			return 0;

		uint8_t* code_buf = (uint8_t*)&code;

		for (uint8_t i = 0; i < 2; i++)
		{
			code_buf[i] += 0x33;
		}

		if (*((uint16_t*)(msg + DL645_DATA_POS)) != code)
			return -1;

		return 0;
	}

	/**
	 * Name:    dlt645_1997_parsing_data
	 * Brief:   DLT645-1997 ���ݰ�����
	 * Input:
	 *  @code:          ��ʶ��
	 *  @read_data:     ���ݰ�ָ��
	 *  @len:           ���ݰ�����
	 *  @real_val:      ���ݴ洢��ַ
	 * Output:  ���ݰ�����
	 */
	int CDriverEMSBoxDLT645_build202528::dlt645_1997_parsing_data(uint32_t code, uint8_t* read_data, uint16_t len, uint8_t* real_val)
	{
		switch (code)
		{
		case DIC_9010:
		case DIC_9020:
		{
			dlt645_data_parse_by_format_to_float(read_data, 4, "XXXXXX.XX", real_val);	
		}
		break;
		case DIC_B611:
		case DIC_B612:
		case DIC_B613:
		case DIC_B691:
		case DIC_B692:
		case DIC_B693:
		{
			dlt645_data_parse_by_format_to_float(read_data, 2, "XXX", real_val);
			break;
		}
		case DIC_B621:
		case DIC_B622:
		case DIC_B623:
		{
			dlt645_data_parse_by_format_to_float(read_data, 2, "XX.XX", real_val);
			break;
		}
		case DIC_B630:
		case DIC_B631:
		case DIC_B632:
		case DIC_B633:
		{
			dlt645_data_parse_by_format_to_float(read_data, 3, "XX.XXXX", real_val);
			break;
		}
		default:
		{
			for (uint16_t i = 0; i < len; i++)
			{
				real_val[i] = ((read_data[i] - 0x33) & 0x0f) + ((read_data[i] - 0x33) >> 4) * 10;
			}
			break;
		}
		}
		return len;
	}

	/**
	 * Name:    dlt645_2007_read_data
	 * Brief:   DLT645-2007 ���ݶ�ȡ
	 * Input:
	 *  @addr:          ��վ��ַ
	 *  @code:          ���ݱ�ʶ
	 *  @read_data:     ���ݴ洢��ַ
	 * Output:  None
	 */
	int CDriverEMSBoxDLT645_build202528::dlt645_2007_read_data(uint32_t code, uint8_t* read_data, const SPointProtocol* pp)
	{
		uint8_t send_buf[DL645_2007_RD_CMD_LEN];
		uint8_t read_buf[DL645_RESP_LEN];

		memset(read_buf, 0, sizeof(read_buf));
		memset(send_buf, 0, sizeof(send_buf));

		memcpy(send_buf + 1, addr_, DL645_ADDR_LEN);

		send_buf[DL645_CONTROL_POS] = C_2007_CODE_RD;
		send_buf[DL645_LEN_POS] = 4;

		uint8_t send_code[4] = { 0 };
		send_code[0] = (code & 0xff) + 0x33;
		send_code[1] = ((code >> 8) & 0xff) + 0x33;
		send_code[2] = ((code >> 16) & 0xff) + 0x33;
		send_code[3] = ((code >> 24) & 0xff) + 0x33;

		memcpy(send_buf + DL645_DATA_POS, send_code, 4);
		if (dlt645_send_msg(send_buf, DL645_2007_RD_CMD_LEN, pp) < 0)
		{
			//m_logCB(ELogLevel::eLogError, "send data error!\n");
			return -1;
		}

		if (dlt645_receive_msg(read_buf, DL645_RESP_LEN, code, 2007, pp) < 0)
		{
			//m_logCB(ELogLevel::eLogError, "receive msg error!\n");
			return -1;
		}
		
		return dlt645_2007_parsing_data(code, read_buf + DL645_DATA_POS + 4, read_buf[DL645_LEN_POS] - 4, read_data);
	}

	/**
	 * Name:    dlt645_2007_recv_check
	 * Brief:   DLT645-2007 ����У��
	 * Input:
	 *  @msg:   У�����ݰ�
	 *  @len:   ���ݰ�����
	 *  @addr:  ��վ��ַ
	 *  @code:  ������
	 * Output:  None
	 */
	int CDriverEMSBoxDLT645_build202528::dlt645_2007_recv_check(uint8_t* msg, int len, uint8_t* addr, uint32_t code)
	{
		if (dlt645_common_check(msg, len, addr) < 0)
		{
			return -1;
		}
		if (msg[DL645_CONTROL_POS] == 0x94)
			return 0;

		uint8_t* code_buf = (uint8_t*)&code;

		for (uint8_t i = 0; i < 4; i++)
		{
			code_buf[i] += 0x33;
		}

		if (*((uint32_t*)(msg + DL645_DATA_POS)) != code)
			return -1;

		return 0;
	}

	/**
	 * Name:    dlt645_2007_parsing_data
	 * Brief:   DLT645-2007 ���ݰ�����
	 * Input:
	 *  @code:          ��ʶ��
	 *  @read_data:     ���ݰ�ָ��
	 *  @len:           ���ݰ�����
	 *  @real_val:      ���ݴ洢��ַ
	 * Output:  ���ݰ�����
	 */
	int CDriverEMSBoxDLT645_build202528::dlt645_2007_parsing_data(uint32_t code, uint8_t* read_data, uint16_t len, uint8_t* real_val)
	{
		switch (code)
		{
		case DIC_0:
		case DIC_100:
		case DIC_200:
		case DIC_300:
		case DIC_400:
		case DIC_10000:
		case DIC_10100:
		case DIC_10200:
		case DIC_10300:
		case DIC_10400:
		case DIC_20000:
		case DIC_20100:
		case DIC_20200:
		case DIC_20300:
		case DIC_20400:
		case DIC_30000:
		case DIC_40000:
		case DIC_50000:
		case DIC_60000:
		case DIC_70000:
		case DIC_80000:
		case DIC_90000:
		{
			dlt645_data_parse_by_format_to_float(read_data, 4, "XXXXXX.XX", real_val);
			break;
		}
		case DIC_2010100:
		case DIC_2010200:
		case DIC_2010300:
		case DIC_20C0100:
		case DIC_20C0200:
		case DIC_20C0300:
		{
			dlt645_data_parse_by_format_to_float(read_data, 2, "XXX.X", real_val);
			break;
		}
		case DIC_2020100:
		case DIC_2020200:
		case DIC_2020300:
		{
			dlt645_data_parse_by_format_to_float(read_data, 3, "XXX.XXX", real_val);
			break;
		}
		case DIC_2030000:
		case DIC_2030100:
		case DIC_2030200:
		case DIC_2030300:
		case DIC_2040000:
		case DIC_2040100:
		case DIC_2040200:
		case DIC_2040300:
		case DIC_2050000:
		case DIC_2050100:
		case DIC_2050200:
		case DIC_2050300:
		{
			dlt645_data_parse_by_format_to_float(read_data, 3, "XX.XXXX", real_val);
			break;
		}
		case DIC_2060000:
		case DIC_2060100:
		case DIC_2060200:
		case DIC_2060300:
		{
			dlt645_data_parse_by_format_to_float(read_data, 2, "X.XXX", real_val);
			break;
		}
		case DIC_2800002:
		{
			dlt645_data_parse_by_format_to_float(read_data, 2, "XX.XX", real_val);
			break;
		}
		case DIC_4000403:
		case DIC_5060101:
		case DIC_7000001:
		case DIC_7000002:
			for (uint16_t i = 0; i < len; i++)
			{
				real_val[i] = read_data[i] - 0x33;
			}
			break;
		default:
			for (uint16_t i = 0; i < len; i++)
			{
				real_val[i] = ((read_data[i] - 0x33) & 0x0f) + ((read_data[i] - 0x33) >> 4) * 10;
			}
			break;
		}
		return len;
	}

	/**
	 * Name:    dlt645_receive_msg
	 * Brief:   645Э����õײ��������
	 * Input:
	 *  @msg:       ���ݰ��洢��ַ
	 *  @len:       �����ճ���
	 *  @addr:      ��վ��ַ
	 *  @code:      ���ݱ�ʶ
	 *  @protocal:  645Э������
	 * Output:  ���ճɹ���0������ʧ�ܣ�-1
	 */
	int CDriverEMSBoxDLT645_build202528::dlt645_receive_msg(uint8_t* msg, uint16_t len, uint32_t code, int version, const SPointProtocol* pp)
	{
		std::vector<uint8_t> data;
		(void)serialPortClient_->recv(data, 1000);
		if (data.empty())
		{
			cb_->onLog(ELogLevel::eLogPacket, UTILS::toIEMSPacketMessage(pp->deviceId, "recv", "no datas"));
			return -1;
		}
		if (data.size() > len || data.size() < 5)
		{
			cb_->onLog(ELogLevel::eLogPacket, UTILS::toIEMSPacketMessage(pp->deviceId, "recv", "error datas : " + bytesToHexString(data.data(), data.size())));
			return -1;
		}
		cb_->onLog(ELogLevel::eLogPacket, UTILS::toIEMSPacketMessage(pp->deviceId, "recv", bytesToHexString(data.data(), data.size())));

		int msg_len = 0;
		if (data[0] == 0xfe && data[1] == 0xfe && data[2] == 0xfe && data[3] == 0xfe)
		{
			memcpy(msg, data.data() + 4, data.size() - 4);
			msg_len = data.size() - 4;
		}
		else
		{
			memcpy(msg, data.data(), data.size());
			msg_len = data.size();
		}

		if (version == 1997)
		{
			return dlt645_1997_recv_check(msg, msg_len, addr_, code);
		}
		else if (version == 2007)
		{
			return dlt645_2007_recv_check(msg, msg_len, addr_, code);
		}
		else
		{
			return -1;
		}
	}

	/**
	 * Name:    dlt645_send_msg
	 * Brief:   645Э����õײ㷢������
	 * Input:
	 *  @msg:       ���͵������׵�ַ
	 *  @len:       ���ͳ���
	 * Output:  ʵ�ʷ��͵��ֽ��������󷵻�-1
	 */
	int CDriverEMSBoxDLT645_build202528::dlt645_send_msg(uint8_t* msg, int len, const SPointProtocol* pp)
	{
		msg[0] = DL645_START_CODE;
		msg[DL645_ADDR_LEN + 1] = DL645_START_CODE;
		msg[len - 1] = DL645_STOP_CODE;
		msg[len - 2] = _crc(msg, len - 2);

		int sendLen = serialPortClient_->send({ msg, msg + len }, 1000);
		cb_->onLog(ELogLevel::eLogPacket, UTILS::toIEMSPacketMessage(pp->deviceId, "send", bytesToHexString(msg, len)));
		return sendLen;
	}

	CDriverEMSBoxDLT645_build202528::CDriverEMSBoxDLT645_build202528()
	{
	}

	CDriverEMSBoxDLT645_build202528::~CDriverEMSBoxDLT645_build202528()
	{
	}

	bool CDriverEMSBoxDLT645_build202528::open(const SProtocolNode& pn)
	{
		SChannelProtocol* channelProtocol = static_cast<SChannelProtocol*>(pn.protocol);

		serialPortClient_ = static_cast<COMM::ICommSerialPort*>(COMM::CFactory::getInstance().produce("CommSerialPort"));
		serialPortClient_->configure(channelProtocol->device, channelProtocol->baud, channelProtocol->dataBit, channelProtocol->stopBit, channelProtocol->parity);
		int ret = serialPortClient_->connect(100);
		printf("dlt645 open %d\n", ret);
		return ret > 0;
	}

	bool CDriverEMSBoxDLT645_build202528::close(const SProtocolNode& pn)
	{
		if (serialPortClient_)
		{
			serialPortClient_->disconnect();
			COMM::CFactory::getInstance().destory(serialPortClient_);
			serialPortClient_ = nullptr;
		}
		return true;
	}

	EStatusCode CDriverEMSBoxDLT645_build202528::control(const IProtocol* const channelProtocol, const IProtocol* const deviceProtocol, const IProtocol* const pointProtocol, const SControlInfo& controlInfo)
	{
		
		return eStatusFail;
	}

	EStatusCode CDriverEMSBoxDLT645_build202528::controlSet(const std::unordered_map<std::string, std::vector<SControlSetInfo>>& controlValues)
	{
		return EStatusCode();
	}

	void CDriverEMSBoxDLT645_build202528::setCallback(IDriverCallback* cb)
	{
		cb_ = cb;
	}

	EStatusCode CDriverEMSBoxDLT645_build202528::poll(const SProtocolNode& pn)
	{
		const SChannelProtocol* const lp = static_cast<const SChannelProtocol* const>(pn.protocol);
		for (const auto& ppn : pn.sub)
		{
			if (!ppn.protocol)
			{
				continue;
			}

			uint8_t read_buf[4] = { 0 };
			int ret = -1;
			SData sdata;

			const SPointProtocol* const pp = static_cast<const SPointProtocol* const>(ppn.protocol);
			dlt645_set_addr(pp->address);
			if (lp->version == 1997)
			{
				ret = dlt645_1997_read_data(pp->code, read_buf, pp);
			}
			else if (lp->version == 2007)
			{
				ret = dlt645_2007_read_data(pp->code, read_buf, pp);
			}
			ret = 1;
			if (ret > 0)
			{
				std::string key = pp->address + "." + std::to_string(pp->code);
				sdata.value = *(float*)read_buf;

				cb_->onData(key, sdata);
			}
		}

		return EStatusCode::eStatusSuccess;
	}

	std::string CDriverEMSBoxDLT645_build202528::getPointKey(const IProtocol* const channelProtocol, const IProtocol* const deviceProtocol, const IProtocol* const pointProtocol)
	{
		//const SDeviceProtocol* const dp = static_cast<const SDeviceProtocol* const>(deviceProtocol);
		if (!pointProtocol)
		{
			return "";
		}
		const SPointProtocol* const pp = static_cast<const SPointProtocol* const>(pointProtocol);
		std::string key;
		key.append(pp->address).append(".").append(std::to_string(pp->code));
		return key;
	}

	IProtocol* CDriverEMSBoxDLT645_build202528::createProtocol(EProtocolType type, OriginProtocol& originProtocol)
	{
		switch (type)
		{
		case EProtocolType::eProtocolLink:
		{
			SChannelProtocol* protocol = new SChannelProtocol;
			protocol->device = originProtocol["DeviceName"];						//The device argument specifies the name of the serial port handled by the OS, eg. "/dev/ttyS0" or "/dev/ttyUSB0". On Windows, it��s necessary to prepend COM name with "\\.\" for COM number greater than 9, eg. "\\\\.\\COM10". See http://msdn.microsoft.com/en-us/library/aa365247(v=vs.85).aspx for details
			protocol->baud = UTILS::str2Number<int>(originProtocol["BaudRate"]);	//The baud argument specifies the baud rate of the communication, eg. 9600, 19200, 57600, 115200, etc.
			protocol->parity = originProtocol["Parity"].empty() ? 'N' : originProtocol["Parity"][0];							//The parity argument can have one of the following values : N for none   E for even  O for odd
			protocol->dataBit = UTILS::str2Number<int>(originProtocol["DataBit"]);	//The data_bits argument specifies the number of bits of data, the allowed values are 5, 6, 7 and 8.
			protocol->stopBit = UTILS::str2Number<int>(originProtocol["StopBit"]);	//The stop_bits argument specifies the bits of stop, the allowed values are 1 and 2.
			
			protocol->version = UTILS::str2Number<uint32_t>(originProtocol["Version"]);  // dlt standard version : 1997/2007
			return protocol;
		}
		case EProtocolType::eProtocolDevice:
		{
			//SDeviceProtocol* protocol = new SDeviceProtocol;
			//protocol->address = originProtocol["Address"];
			return nullptr;
		}
		case EProtocolType::eProtocolPoint:
		{
			if (originProtocol["Address"].empty() || originProtocol["Code"].empty())
			{
				return nullptr;
			}

			SPointProtocol* protocol = new SPointProtocol;
			protocol->address = originProtocol["Address"];
			protocol->code = UTILS::str2Number<uint32_t>(originProtocol["Code"]);
			protocol->deviceId = originProtocol["_device"];
			return protocol;
		}
		default:
			break;
		}
		return nullptr;
	}

	void CDriverEMSBoxDLT645_build202528::destoryProtocol(EProtocolType type, IProtocol* protocol)
	{
		if (protocol)
		{
			delete protocol;
		}
	}

	void CDriverEMSBoxDLT645_build202528::setFlag(EFlag flag)
	{

	}
	
} //namespace DRIVER

extern "C"
{
	DRIVER_API DRIVER::IDriver* createDriver()
	{
		return new DRIVER::CDriverEMSBoxDLT645_build202528;
	}

	DRIVER_API void destoryDriver(DRIVER::IDriver* p)
	{
		if (p) { delete p; }
	}
}
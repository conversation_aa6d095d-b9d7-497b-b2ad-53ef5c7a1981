#pragma once

#define SYSTEM_SETTING_DLL_BUILD

#if defined(SYSTEM_SETTING_DLL_BUILD)
#if defined(_MSC_VER) || defined(__MINGW32__)
#define SYSTEM_SETTING_API __declspec(dllexport)
#elif defined(__GNUC__) || defined(__clang__)
#define SYSTEM_SETTING_API __attribute__((visibility("default")))
#endif // if defined(_MSC_VER)

#elif defined(SYSTEM_SETTING_DLL)
#if defined(_MSC_VER) || defined(__MINGW32__)
#define SYSTEM_SETTING_API __declspec(dllimport)
#endif // if defined(_MSC_VER)
#endif // ifdef SYSTEM_SETTING_DLL_BUILD

#include <string>
#include <vector>
#include <cstdint>

struct SNetworkConfig
{
	enum EType
	{
		eUnKnow,
		eEhernet,
		// 注释掉 WLAN 类型枚举
		// e<PERSON><PERSON>an,
		ePP<PERSON>,
		eLocalLoopback,
	};

	//bool enable;
	EType type;
	std::string name;
	std::string ipv4;
	std::string netmask;
	std::string mac;
	std::string ipv6;
	std::string dns;
	std::string gateway;
	uint64_t rxPackets;
	uint64_t rxBytes;
	uint64_t txPackets;
	uint64_t txBytes;
	bool linkDetected;
	bool dhcp;
	bool enableChange;
	//wireless
	std::string ssid;
};
using NetworkConfigList = std::vector<SNetworkConfig>;

// unit KB
struct SMemoryInfo
{
	uint64_t memTotal;
	uint64_t memFree;
	uint64_t memUsed;

	uint64_t swapTotal;
	uint64_t swapFree;
	uint64_t swapUsed;
	SMemoryInfo():memTotal(0),memFree(0),memUsed(0),swapTotal(0),swapFree(0),swapUsed(0){}
};

// unit KB
struct SStorageInfo
{
	std::string name;
	uint64_t capacity;
	uint64_t free;
	uint64_t used;
	SStorageInfo():name(std::string()),capacity(0),free(0),used(0){}
};
using StorageInfoList = std::vector<SStorageInfo>;

struct SProcessInfo
{
	std::string name;
	uint64_t pid;
	uint64_t ppid;
	float memoryUsage;
	float cpuUsage;
	uint64_t time;
};
using ProcessInfoList = std::vector<SProcessInfo>;

struct SWirelessInfo
{
	std::string ssid;
	std::string flags;
	int signal;
};
using WirelessInfoList = std::vector<SWirelessInfo>;

struct SRouteInfo
{
	std::string destination;
	std::string gateway;
	std::string genmask;
	std::string iface;
};
using RouteInfoList = std::vector<SRouteInfo>;

struct SCPUInfo
{
	double usage;
	SCPUInfo():usage(0.0f){}
};

struct STimeSyncInfo
{
	std::string address;
	bool Switch;
};

class ISystemSetting
{
public:
	ISystemSetting() {}
	virtual ~ISystemSetting() {}

	virtual bool init(const std::string& folderPath) = 0;

	virtual bool uninit() = 0;

	virtual std::string getLastError() = 0;

	virtual bool getSerialNumber(const std::string& sn) = 0;

	virtual bool enableNetwork(const std::string& name, bool enable) = 0;

	virtual bool getNetworkConfig(const std::string& name, SNetworkConfig& networkConfig) = 0;

	virtual bool getNetworkConfigList(std::vector<SNetworkConfig>& networkConfigList) = 0;

	virtual bool getMemoryInfo(SMemoryInfo& memoryInfo) = 0;

	virtual bool getStorageInfo(const std::string& name, SStorageInfo& storageInfo) = 0;

	virtual bool getStorageInfoList(StorageInfoList& storageInfoList) = 0;

	virtual bool getProcessInfo(const std::string& name, SProcessInfo& processInfo) = 0;

	virtual bool getProcessInfoList(ProcessInfoList& processInfoList) = 0;

	virtual bool getAvailableWirelessList(const std::string& args, WirelessInfoList& wirelessInfoList) = 0;

	virtual bool getSavedWirelessList(WirelessInfoList& wirelessInfoList) = 0;

	virtual bool getRouteInfoList(RouteInfoList& routeInfoList) = 0;

	virtual bool getCPUInfo(SCPUInfo& cpuInfo) = 0;

	virtual bool getCurrentStorageInfo(SStorageInfo& storageInfo) = 0;

	virtual bool getProductNumber(std::string& pn) = 0;

	virtual bool setNetWorkConfig(const std::string& args) = 0;

	virtual bool setWirelessConnect(const std::string& args) = 0;

	virtual bool setWirelessConfig(const std::string& args) = 0;

	virtual bool setWirelessDisconnect(const std::string& args) = 0;

	virtual bool setDnsConfig(const std::string& args) = 0;

	virtual bool getSyncTime(STimeSyncInfo& timeSyncInfo) = 0;

	virtual bool setSyncTime(const std::string& args) = 0;

	virtual bool doSyncTime(const std::string& args) = 0;

};

extern "C"
{
	SYSTEM_SETTING_API ISystemSetting* create();

	SYSTEM_SETTING_API void destory(ISystemSetting* p);
}

#include "SystemSetting.h"

#include <stdio.h>
#include <iostream>

#include "../Utils/String.hpp"
#include "json/json.h"

// Windows platform specific header files
#ifdef _WIN32
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iphlpapi.h>
#include <psapi.h>
#include <pdh.h>
#include <pdhmsg.h>
#include <tlhelp32.h>
#include <winbase.h>
// 注释掉 wlanapi.h 头文件包含
// #include <wlanapi.h>
#include <objbase.h>
#include <wtypes.h>
#include <setupapi.h>
#include <devguid.h>
#include <cfgmgr32.h>
#include <shlwapi.h>
#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "pdh.lib")
// 注释掉 wlanapi.lib 库链接
// #pragma comment(lib, "wlanapi.lib")
#pragma comment(lib, "ole32.lib")
#pragma comment(lib, "setupapi.lib")
#pragma comment(lib, "shlwapi.lib")
#endif

static bool toJsonValue(const std::string& str,Json::Value& root)
{
    JSONCPP_STRING err;
    Json::CharReaderBuilder builder;
    std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
    if(!reader->parse(str.c_str(), str.c_str() + str.size(), &root, &err))
	{
		return false;
	}
    return true;
}

#ifdef _WIN32
// Windows helper functions
static std::string WStringToString(const std::wstring& wstr)
{
    if (wstr.empty()) return std::string();
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

static std::wstring StringToWString(const std::string& str)
{
    if (str.empty()) return std::wstring();
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

// Check if running on Windows Server
static bool IsWindowsServer()
{
    OSVERSIONINFOEX osvi;
    DWORDLONG dwlConditionMask = 0;
    
    ZeroMemory(&osvi, sizeof(OSVERSIONINFOEX));
    osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFOEX);
    osvi.wProductType = VER_NT_SERVER;
    
    VER_SET_CONDITION(dwlConditionMask, VER_PRODUCT_TYPE, VER_EQUAL);
    
    return VerifyVersionInfo(&osvi, VER_PRODUCT_TYPE, dwlConditionMask);
}

// Get Windows network adapter information
static bool GetWindowsNetworkInfo(std::vector<SNetworkConfig>& networkConfigList)
{
    // 在Windows Server上不执行
    if (IsWindowsServer()) {
        return false;
    }

    ULONG outBufLen = 15000;
    PIP_ADAPTER_ADDRESSES pAddresses = nullptr;
    DWORD dwRetVal = 0;

    do {
        pAddresses = (IP_ADAPTER_ADDRESSES*)malloc(outBufLen);
        if (pAddresses == nullptr) {
            return false;
        }

        dwRetVal = GetAdaptersAddresses(AF_UNSPEC,
            GAA_FLAG_INCLUDE_PREFIX | GAA_FLAG_INCLUDE_GATEWAYS,
            nullptr, pAddresses, &outBufLen);

        if (dwRetVal == ERROR_BUFFER_OVERFLOW) {
            free(pAddresses);
            pAddresses = nullptr;
        } else {
            break;
        }
    } while (dwRetVal == ERROR_BUFFER_OVERFLOW);

    if (dwRetVal != NO_ERROR) {
        if (pAddresses) free(pAddresses);
        return false;
    }

    PIP_ADAPTER_ADDRESSES pCurrAddresses = pAddresses;
    while (pCurrAddresses) {
        SNetworkConfig config;
        
        // Adapter name
        config.name = WStringToString(pCurrAddresses->FriendlyName);
        
        // Adapter type
        switch (pCurrAddresses->IfType) {
            case IF_TYPE_ETHERNET_CSMACD:
                config.type = SNetworkConfig::EType::eEhernet;
                break;
            case IF_TYPE_IEEE80211:
                // 注释掉 WLAN 类型设置
                // config.type = SNetworkConfig::EType::eWLan;
                config.type = SNetworkConfig::EType::eUnKnow;
                break;
            case IF_TYPE_PPP:
                config.type = SNetworkConfig::EType::ePPP;
                break;
            case IF_TYPE_SOFTWARE_LOOPBACK:
                config.type = SNetworkConfig::EType::eLocalLoopback;
                break;
            default:
                config.type = SNetworkConfig::EType::eUnKnow;
                break;
        }

        // MAC address
        if (pCurrAddresses->PhysicalAddressLength != 0) {
            char mac[18] = {0};
            for (DWORD i = 0; i < pCurrAddresses->PhysicalAddressLength; i++) {
                if (i == (pCurrAddresses->PhysicalAddressLength - 1)) {
                    sprintf_s(mac + strlen(mac), sizeof(mac) - strlen(mac), "%.2X", (int)pCurrAddresses->PhysicalAddress[i]);
                } else {
                    sprintf_s(mac + strlen(mac), sizeof(mac) - strlen(mac), "%.2X:", (int)pCurrAddresses->PhysicalAddress[i]);
                }
            }
            config.mac = mac;
        }

        // IP address
        PIP_ADAPTER_UNICAST_ADDRESS pUnicast = pCurrAddresses->FirstUnicastAddress;
        while (pUnicast) {
            if (pUnicast->Address.lpSockaddr->sa_family == AF_INET) {
                // IPv4
                char ip[INET_ADDRSTRLEN];
                inet_ntop(AF_INET, &((struct sockaddr_in*)pUnicast->Address.lpSockaddr)->sin_addr, ip, INET_ADDRSTRLEN);
                config.ipv4 = ip;
                
                // Subnet mask
                ULONG mask = 0;
                ConvertLengthToIpv4Mask(pUnicast->OnLinkPrefixLength, &mask);
                struct in_addr maskAddr;
                maskAddr.S_un.S_addr = mask;
                char maskStr[INET_ADDRSTRLEN];
                inet_ntop(AF_INET, &maskAddr, maskStr, INET_ADDRSTRLEN);
                config.netmask = maskStr;
            } else if (pUnicast->Address.lpSockaddr->sa_family == AF_INET6) {
                // IPv6
                char ip[INET6_ADDRSTRLEN];
                inet_ntop(AF_INET6, &((struct sockaddr_in6*)pUnicast->Address.lpSockaddr)->sin6_addr, ip, INET6_ADDRSTRLEN);
                config.ipv6 = ip;
            }
            pUnicast = pUnicast->Next;
        }

        // Gateway
        PIP_ADAPTER_GATEWAY_ADDRESS pGateway = pCurrAddresses->FirstGatewayAddress;
        if (pGateway && pGateway->Address.lpSockaddr->sa_family == AF_INET) {
            char gateway[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &((struct sockaddr_in*)pGateway->Address.lpSockaddr)->sin_addr, gateway, INET_ADDRSTRLEN);
            config.gateway = gateway;
        }

        // Statistics - need to use GetIfTable2 to get
        config.rxBytes = 0;
        config.txBytes = 0;
        config.rxPackets = 0;
        config.txPackets = 0;
        
        // Try to get interface statistics
        PMIB_IF_TABLE2 pIfTable = nullptr;
        if (GetIfTable2(&pIfTable) == NO_ERROR) {
            for (ULONG i = 0; i < pIfTable->NumEntries; i++) {
                if (pIfTable->Table[i].InterfaceIndex == pCurrAddresses->IfIndex) {
                    config.rxBytes = pIfTable->Table[i].InOctets;
                    config.txBytes = pIfTable->Table[i].OutOctets;
                    config.rxPackets = pIfTable->Table[i].InUcastPkts + pIfTable->Table[i].InNUcastPkts;
                    config.txPackets = pIfTable->Table[i].OutUcastPkts + pIfTable->Table[i].OutNUcastPkts;
                    break;
                }
            }
            FreeMibTable(pIfTable);
        }

        // Connection status
        config.linkDetected = (pCurrAddresses->OperStatus == IfOperStatusUp);
        config.dhcp = (pCurrAddresses->Dhcpv4Server.iSockaddrLength > 0);
        config.enableChange = true;

        networkConfigList.push_back(config);
        pCurrAddresses = pCurrAddresses->Next;
    }

    if (pAddresses) {
        free(pAddresses);
    }
    return true;
}

// Get Windows memory information
static bool GetWindowsMemoryInfo(SMemoryInfo& memoryInfo)
{
    MEMORYSTATUSEX memStatus;
    memStatus.dwLength = sizeof(memStatus);
    if (!GlobalMemoryStatusEx(&memStatus)) {
        return false;
    }

    // Convert to KB
    memoryInfo.memTotal = memStatus.ullTotalPhys / 1024;
    memoryInfo.memFree = memStatus.ullAvailPhys / 1024;
    memoryInfo.memUsed = memoryInfo.memTotal - memoryInfo.memFree;
    
    memoryInfo.swapTotal = memStatus.ullTotalPageFile / 1024;
    memoryInfo.swapFree = memStatus.ullAvailPageFile / 1024;
    memoryInfo.swapUsed = memoryInfo.swapTotal - memoryInfo.swapFree;

    return true;
}

// Get Windows storage information
static bool GetWindowsStorageInfo(StorageInfoList& storageInfoList)
{
    DWORD drives = GetLogicalDrives();
    char driveLetter = 'A';
    
    for (int i = 0; i < 26; i++) {
        if (drives & (1 << i)) {
            char drivePath[4] = {driveLetter, ':', '\\', '\0'};
            
            ULARGE_INTEGER freeBytesAvailable, totalNumberOfBytes, totalNumberOfFreeBytes;
            if (GetDiskFreeSpaceExA(drivePath, &freeBytesAvailable, &totalNumberOfBytes, &totalNumberOfFreeBytes)) {
                SStorageInfo info;
                info.name = drivePath;
                info.capacity = totalNumberOfBytes.QuadPart / 1024; // Convert to KB
                info.free = totalNumberOfFreeBytes.QuadPart / 1024;
                info.used = info.capacity - info.free;
                storageInfoList.push_back(info);
            }
        }
        driveLetter++;
    }
    return true;
}

// Get Windows CPU information
static bool GetWindowsCPUInfo(SCPUInfo& cpuInfo)
{
    static PDH_HQUERY cpuQuery = nullptr;
    static PDH_HCOUNTER cpuTotal = nullptr;
    static bool initialized = false;

    if (!initialized) {
        PdhOpenQuery(NULL, NULL, &cpuQuery);
        PdhAddEnglishCounterA(cpuQuery, "\\Processor(_Total)\\% Processor Time", NULL, &cpuTotal);
        PdhCollectQueryData(cpuQuery);
        initialized = true;
        Sleep(1000); // Wait 1 second for accurate CPU usage
    }

    PdhCollectQueryData(cpuQuery);
    PDH_FMT_COUNTERVALUE counterVal;
    PdhGetFormattedCounterValue(cpuTotal, PDH_FMT_DOUBLE, NULL, &counterVal);
    cpuInfo.usage = counterVal.doubleValue;

    return true;
}

// Get Windows process information
static bool GetWindowsProcessInfo(ProcessInfoList& processInfoList)
{
    // 在Windows Server上不执行
    if (IsWindowsServer()) {
        return false;
    }

    HANDLE hProcessSnap;
    PROCESSENTRY32 pe32;

    hProcessSnap = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hProcessSnap == INVALID_HANDLE_VALUE) {
        return false;
    }

    pe32.dwSize = sizeof(PROCESSENTRY32);

    if (!Process32First(hProcessSnap, &pe32)) {
        CloseHandle(hProcessSnap);
        return false;
    }

    do {
        SProcessInfo info;
        // Convert TCHAR to string properly
        #ifdef UNICODE
        info.name = WStringToString(pe32.szExeFile);
        #else
        info.name = pe32.szExeFile;
        #endif
        info.pid = pe32.th32ProcessID;
        info.ppid = pe32.th32ParentProcessID;
        
        // Get process memory and CPU usage requires opening process handle
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, pe32.th32ProcessID);
        if (hProcess) {
            PROCESS_MEMORY_COUNTERS pmc;
            if (GetProcessMemoryInfo(hProcess, &pmc, sizeof(pmc))) {
                info.memoryUsage = (float)(pmc.WorkingSetSize / 1024.0 / 1024.0); // MB
            }
            CloseHandle(hProcess);
        }
        
        processInfoList.push_back(info);
    } while (Process32Next(hProcessSnap, &pe32));

    CloseHandle(hProcessSnap);
    return true;
}

// Get Windows wireless network list
static bool GetWindowsWirelessList(WirelessInfoList& wirelessInfoList)
{
    // 在Windows Server上不执行
    if (IsWindowsServer()) {
        return false;
    }

    // 注释掉整个 WLAN API 相关的无线网络列表获取功能
    /*
    HANDLE hClient = nullptr;
    DWORD dwMaxClient = 2;
    DWORD dwCurVersion = 0;
    DWORD dwResult = 0;

    dwResult = WlanOpenHandle(dwMaxClient, nullptr, &dwCurVersion, &hClient);
    if (dwResult != ERROR_SUCCESS) {
        return false;
    }

    PWLAN_INTERFACE_INFO_LIST pIfList = nullptr;
    dwResult = WlanEnumInterfaces(hClient, nullptr, &pIfList);
    if (dwResult != ERROR_SUCCESS) {
        WlanCloseHandle(hClient, nullptr);
        return false;
    }

    for (DWORD i = 0; i < pIfList->dwNumberOfItems; i++) {
        PWLAN_AVAILABLE_NETWORK_LIST pBssList = nullptr;
        dwResult = WlanGetAvailableNetworkList(hClient, &pIfList->InterfaceInfo[i].InterfaceGuid,
            0, nullptr, &pBssList);
        
        if (dwResult == ERROR_SUCCESS) {
            for (DWORD j = 0; j < pBssList->dwNumberOfItems; j++) {
                SWirelessInfo info;
                info.ssid = (char*)pBssList->Network[j].dot11Ssid.ucSSID;
                info.signal = pBssList->Network[j].wlanSignalQuality;
                
                // Security flags
                if (pBssList->Network[j].bSecurityEnabled) {
                    info.flags = "WPA/WPA2";
                } else {
                    info.flags = "Open";
                }
                
                wirelessInfoList.push_back(info);
            }
            WlanFreeMemory(pBssList);
        }
    }

    WlanFreeMemory(pIfList);
    WlanCloseHandle(hClient, nullptr);
    */
    
    // 返回空列表，表示没有无线网络
    return false;
}
#endif

CSystemSetting::CSystemSetting() : dynLibs_(nullptr)
{
}

CSystemSetting::~CSystemSetting()
{
}

bool CSystemSetting::init(const std::string& folderPath)
{
	dynLibs_ = new CDynamicLibrary;
	std::string systemType = "General";
	dynLibs_->regist("getSerialNumber", "./SystemSettingExt/"+ systemType + "/","getSerialNumber_" + systemType);
	dynLibs_->regist("enableNetwork", "./SystemSettingExt/" + systemType + "/", "enableNetwork_" + systemType);
	dynLibs_->regist("getNetworkConfig", "./SystemSettingExt/" + systemType + "/", "getNetworkConfig_" + systemType);
	dynLibs_->regist("getNetworkConfigList", "./SystemSettingExt/" + systemType + "/", "getNetworkConfigList_" + systemType);
	dynLibs_->regist("getMemoryInfo", "./SystemSettingExt/" + systemType + "/", "getMemoryInfo_" + systemType);
	dynLibs_->regist("getStorageInfo", "./SystemSettingExt/" + systemType + "/", "getStorageInfo_" + systemType);
	dynLibs_->regist("getStorageInfoList", "./SystemSettingExt/" + systemType + "/", "getStorageInfoList_" + systemType);
	dynLibs_->regist("getProcessInfo", "./SystemSettingExt/" + systemType + "/", "getProcessInfo_" + systemType);
	dynLibs_->regist("getProcessInfoList", "./SystemSettingExt/" + systemType + "/", "getProcessInfoList_" + systemType);
	dynLibs_->regist("getAvailableWirelessList", "./SystemSettingExt/" + systemType + "/", "getAvailableWirelessList_" + systemType);
	dynLibs_->regist("getSavedWirelessList", "./SystemSettingExt/" + systemType + "/", "getSavedWirelessList_" + systemType);
	dynLibs_->regist("getRouteInfoList", "./SystemSettingExt/" + systemType + "/", "getRouteInfoList_" + systemType);
	dynLibs_->regist("getCPUInfo", "./SystemSettingExt/" + systemType + "/", "getCPUInfo_" + systemType);
	dynLibs_->regist("getCurrentStorageInfo", "./SystemSettingExt/" + systemType + "/", "getCurrentStorageInfo_" + systemType);
	dynLibs_->regist("getProductNumber", "./SystemSettingExt/" + systemType + "/", "getProductNumber" + systemType);
	dynLibs_->regist("setNetWorkConfig", "./SystemSettingExt/" + systemType + "/", "setNetWorkConfig" + systemType);
	dynLibs_->regist("setWirelessConnect", "./SystemSettingExt/" + systemType + "/", "setWirelessConnect" + systemType);
	dynLibs_->regist("setWirelessConfig", "./SystemSettingExt/" + systemType + "/", "setWirelessConfig" + systemType);
	dynLibs_->regist("setWirelessDisconnect", "./SystemSettingExt/" + systemType + "/", "setWirelessDisconnect" + systemType);
	dynLibs_->regist("setDnsConfig", "./SystemSettingExt/" + systemType + "/", "setDnsConfig" + systemType);
	dynLibs_->regist("setSyncTime", "./SystemSettingExt/" + systemType + "/", "setSyncTime" + systemType);
	dynLibs_->regist("getSyncTime", "./SystemSettingExt/" + systemType + "/", "getSyncTime" + systemType);
	dynLibs_->regist("doSyncTime", "./SystemSettingExt/" + systemType + "/", "doSyncTime" + systemType);


	systemType = folderPath;
	dynLibs_->regist("getSerialNumber", "./SystemSettingExt/" + systemType + "/", "getSerialNumber_" + systemType);
	dynLibs_->regist("enableNetwork", "./SystemSettingExt/" + systemType + "/", "enableNetwork_" + systemType);
	dynLibs_->regist("getNetworkConfig", "./SystemSettingExt/" + systemType + "/", "getNetworkConfig_" + systemType);
	dynLibs_->regist("getNetworkConfigList", "./SystemSettingExt/" + systemType + "/", "getNetworkConfigList_" + systemType);
	dynLibs_->regist("getMemoryInfo", "./SystemSettingExt/" + systemType + "/", "getMemoryInfo_" + systemType);
	dynLibs_->regist("getStorageInfo", "./SystemSettingExt/" + systemType + "/", "getStorageInfo_" + systemType);
	dynLibs_->regist("getStorageInfoList", "./SystemSettingExt/" + systemType + "/", "getStorageInfoList_" + systemType);
	dynLibs_->regist("getProcessInfo", "./SystemSettingExt/" + systemType + "/", "getProcessInfo_" + systemType);
	dynLibs_->regist("getProcessInfoList", "./SystemSettingExt/" + systemType + "/", "getProcessInfoList_" + systemType);
	dynLibs_->regist("getAvailableWirelessList", "./SystemSettingExt/" + systemType + "/", "getAvailableWirelessList_" + systemType);
	dynLibs_->regist("getSavedWirelessList", "./SystemSettingExt/" + systemType + "/", "getSavedWirelessList_" + systemType);
	dynLibs_->regist("getRouteInfoList", "./SystemSettingExt/" + systemType + "/", "getRouteInfoList_" + systemType);
	dynLibs_->regist("getCPUInfo", "./SystemSettingExt/" + systemType + "/", "getCPUInfo_" + systemType);
	dynLibs_->regist("getCurrentStorageInfo", "./SystemSettingExt/" + systemType + "/", "getCurrentStorageInfo_" + systemType);
	dynLibs_->regist("getProductNumber", "./SystemSettingExt/" + systemType + "/", "getProductNumber" + systemType);
	dynLibs_->regist("setNetWorkConfig", "./SystemSettingExt/" + systemType + "/", "setNetWorkConfig" + systemType);
	dynLibs_->regist("setWirelessConnect", "./SystemSettingExt/" + systemType + "/", "setWirelessConnect" + systemType);
	dynLibs_->regist("setWirelessConfig", "./SystemSettingExt/" + systemType + "/", "setWirelessConfig" + systemType);
	dynLibs_->regist("setWirelessDisconnect", "./SystemSettingExt/" + systemType + "/", "setWirelessDisconnect" + systemType);
	dynLibs_->regist("setDnsConfig", "./SystemSettingExt/" + systemType + "/", "setDnsConfig" + systemType);
	dynLibs_->regist("setSyncTime", "./SystemSettingExt/" + systemType + "/", "setSyncTime" + systemType);
	dynLibs_->regist("getSyncTime", "./SystemSettingExt/" + systemType + "/", "getSyncTime" + systemType);
	dynLibs_->regist("doSyncTime", "./SystemSettingExt/" + systemType + "/", "doSyncTime" + systemType);

	folderPath_ = "./SystemSettingExt/" + folderPath + "/";
	shell_ = new CShell;
	systemType = "General";
	shell_->regist("getSerialNumber", "./SystemSettingExt/" + systemType + "/", "getSerialNumber");
	shell_->regist("enableNetwork", "./SystemSettingExt/" + systemType + "/", "enableNetwork");
	shell_->regist("getNetworkConfig", "./SystemSettingExt/" + systemType + "/", "getNetworkConfig");
	shell_->regist("getNetworkConfigList", "./SystemSettingExt/" + systemType + "/", "getNetworkConfigList");
	shell_->regist("getMemoryInfo", "./SystemSettingExt/" + systemType + "/", "getMemoryInfo");
	shell_->regist("getStorageInfo", "./SystemSettingExt/" + systemType + "/", "getStorageInfo");
	shell_->regist("getStorageInfoList", "./SystemSettingExt/" + systemType + "/", "getStorageInfoList");
	shell_->regist("getProcessInfo", "./SystemSettingExt/" + systemType + "/", "getProcessInfo");
	shell_->regist("getProcessInfoList", "./SystemSettingExt/" + systemType + "/", "getProcessInfoList");
	shell_->regist("getAvailableWirelessList", "./SystemSettingExt/" + systemType + "/", "getAvailableWirelessList");
	shell_->regist("getSavedWirelessList", "./SystemSettingExt/" + systemType + "/", "getSavedWirelessList");
	shell_->regist("getRouteInfoList", "./SystemSettingExt/" + systemType + "/", "getRouteInfoList");
	shell_->regist("getCPUInfo", "./SystemSettingExt/" + systemType + "/", "getCPUInfo");
	shell_->regist("getCurrentStorageInfo", "./SystemSettingExt/" + systemType + "/", "getCurrentStorageInfo");
	shell_->regist("getProductNumber", "./SystemSettingExt/" + systemType + "/", "getProductNumber");
	shell_->regist("setNetWorkConfig", "./SystemSettingExt/" + systemType + "/", "setNetWorkConfig");
	shell_->regist("setWirelessConnect", "./SystemSettingExt/" + systemType + "/", "setWirelessConnect");
	shell_->regist("setWirelessConfig", "./SystemSettingExt/" + systemType + "/", "setWirelessConfig");
	shell_->regist("setWirelessDisconnect", "./SystemSettingExt/" + systemType + "/", "setWirelessDisconnect");
	shell_->regist("setDnsConfig", "./SystemSettingExt/" + systemType + "/", "setDnsConfig");
	shell_->regist("setSyncTime", "./SystemSettingExt/" + systemType + "/", "setSyncTime");
	shell_->regist("getSyncTime", "./SystemSettingExt/" + systemType + "/", "getSyncTime");
	shell_->regist("doSyncTime", "./SystemSettingExt/" + systemType + "/", "doSyncTime");

	systemType = folderPath;
	shell_->regist("getSerialNumber", "./SystemSettingExt/" + systemType + "/", "getSerialNumber");
	shell_->regist("enableNetwork", "./SystemSettingExt/" + systemType + "/", "enableNetwork");
	shell_->regist("getNetworkConfig", "./SystemSettingExt/" + systemType + "/", "getNetworkConfig");
	shell_->regist("getNetworkConfigList", "./SystemSettingExt/" + systemType + "/", "getNetworkConfigList");
	shell_->regist("getMemoryInfo", "./SystemSettingExt/" + systemType + "/", "getMemoryInfo");
	shell_->regist("getStorageInfo", "./SystemSettingExt/" + systemType + "/", "getStorageInfo");
	shell_->regist("getStorageInfoList", "./SystemSettingExt/" + systemType + "/", "getStorageInfoList");
	shell_->regist("getProcessInfo", "./SystemSettingExt/" + systemType + "/", "getProcessInfo");
	shell_->regist("getProcessInfoList", "./SystemSettingExt/" + systemType + "/", "getProcessInfoList");
	shell_->regist("getAvailableWirelessList", "./SystemSettingExt/" + systemType + "/", "getAvailableWirelessList");
	shell_->regist("getSavedWirelessList", "./SystemSettingExt/" + systemType + "/", "getSavedWirelessList");
	shell_->regist("getRouteInfoList", "./SystemSettingExt/" + systemType + "/", "getRouteInfoList");
	shell_->regist("getCPUInfo", "./SystemSettingExt/" + systemType + "/", "getCPUInfo");
	shell_->regist("getCurrentStorageInfo", "./SystemSettingExt/" + systemType + "/", "getCurrentStorageInfo");
	shell_->regist("getProductNumber", "./SystemSettingExt/" + systemType + "/", "getProductNumber");
	shell_->regist("setNetWorkConfig", "./SystemSettingExt/" + systemType + "/", "setNetWorkConfig");
	shell_->regist("setWirelessConnect", "./SystemSettingExt/" + systemType + "/", "setWirelessConnect");
	shell_->regist("setWirelessConfig", "./SystemSettingExt/" + systemType + "/", "setWirelessConfig");
	shell_->regist("setWirelessDisconnect", "./SystemSettingExt/" + systemType + "/", "setWirelessDisconnect");
	shell_->regist("setDnsConfig", "./SystemSettingExt/" + systemType + "/", "setDnsConfig");
	shell_->regist("setSyncTime", "./SystemSettingExt/" + systemType + "/", "setSyncTime");
	shell_->regist("getSyncTime", "./SystemSettingExt/" + systemType + "/", "getSyncTime");
	shell_->regist("doSyncTime", "./SystemSettingExt/" + systemType + "/", "doSyncTime");

	return true;
}

bool CSystemSetting::uninit()
{
	if (dynLibs_)
	{
		delete dynLibs_;
		dynLibs_ = nullptr;
	}

	if (shell_)
	{
		delete shell_;
		shell_ = nullptr;
	}
	
	return true;
}

std::string CSystemSetting::getLastError()
{
	return "true";
}

bool CSystemSetting::getSerialNumber(const std::string& sn)
{
	return false;
}

bool CSystemSetting::enableNetwork(const std::string& name, bool enable)
{
	bool (*enableNetwork)() = nullptr;
	dynLibs_->getFunctionPointer("enableNetwork", "enableNetwork", (void*&)enableNetwork);
	if (enableNetwork)
	{
		enableNetwork();
	}
	else
	{
#ifdef _WIN32
		// 在Windows Server上不执行
		if (!IsWindowsServer()) {
			// Windows platform network enable/disable requires administrator privileges
			// Basic implementation provided here, may need to use netsh command in practice
			std::string cmd = "netsh interface set interface \"" + name + "\" " + (enable ? "enabled" : "disabled");
			system(cmd.c_str());
		}
#else
		// Linux platform uses shell scripts
		std::string output;
		shell_->execute("enableNetwork", name, output);
#endif
	}

	return true;
}

bool CSystemSetting::getNetworkConfig(const std::string& name, SNetworkConfig& networkConfig)
{
	bool (*getNetworkConfig)(const char*, void*) = nullptr;
	dynLibs_->getFunctionPointer("getNetworkConfig", "getNetworkConfig", (void*&)getNetworkConfig);
	if (getNetworkConfig)
	{
		return getNetworkConfig(name.c_str(), (void*) & networkConfig);
	}
	else
	{
#ifdef _WIN32
		// 在Windows Server上不执行
		if (IsWindowsServer()) {
			return false;
		}
		// Windows platform uses API to get network configuration
		std::vector<SNetworkConfig> networkList;
		if (GetWindowsNetworkInfo(networkList)) {
			for (const auto& config : networkList) {
				if (config.name == name) {
					networkConfig = config;
					return true;
				}
			}
		}
		return false;
#else
		// Linux platform uses shell scripts
		std::string output;
		std::string args = " " + name;
		shell_->execute("getNetworkConfig", args, output);
		// std::cout<<output<<std::endl;
		Json::Value root;
		if(!toJsonValue(output,root))
		{
			return false;
		}

		switch(root["type"].asUInt64())
		{
			case 1:
				networkConfig.type = SNetworkConfig::EType::eEhernet;
				break;
			case 2:
				// 注释掉 WLAN 类型设置
				// networkConfig.type = SNetworkConfig::EType::eWLan;
				networkConfig.type = SNetworkConfig::EType::eUnKnow;
				break;
			case 512:
				networkConfig.type = SNetworkConfig::EType::ePPP;
				break;
			case 772:
				networkConfig.type = SNetworkConfig::EType::eLocalLoopback;
				break;
			default:
				networkConfig.type = SNetworkConfig::EType::eUnKnow;
				break;
			}
		networkConfig.name = root["name"].asString();
		networkConfig.ipv4 = root["ipv4"].asString();
		networkConfig.netmask = root["netmask"].asString();
		networkConfig.mac = root["mac"].asString();
		networkConfig.ipv6 = root["ipv6"].asString();
		networkConfig.dns = root["dns"].asString();
		networkConfig.gateway = root["gateway"].asString();
		networkConfig.rxPackets = root["rxPackets"].asUInt64();
		networkConfig.rxBytes = root["rxBytes"].asUInt64();
		networkConfig.txPackets = root["txPackets"].asUInt64();
		networkConfig.txBytes = root["txBytes"].asUInt64();
		networkConfig.linkDetected = root["linkDetected"].asString() == "yes" ? true: false;
		networkConfig.dhcp = root["dhcp"].asString() == "yes" ? true : false;
		networkConfig.enableChange = root["enableChange"].asString() == "yes" ? true : false;
#endif
	}
	return true;
}

bool CSystemSetting::getNetworkConfigList(std::vector<SNetworkConfig>& networkConfigList)
{
	bool (*getNetworkConfigList)(void*) = nullptr;
	dynLibs_->getFunctionPointer("getNetworkConfigList", "getNetworkConfigList", (void*&)getNetworkConfigList);
	if (getNetworkConfigList)
	{
		return getNetworkConfigList((void*) & getNetworkConfigList);
	}
	else
	{
#ifdef _WIN32
		// 在Windows Server上不执行
		if (IsWindowsServer()) {
			return false;
		}
		// Windows platform uses API to get network configuration list
		return GetWindowsNetworkInfo(networkConfigList);
#else
		// Linux platform uses shell scripts
		std::string output;
		std::string arg;
		shell_->execute("getNetworkConfigList", arg, output);
		// std::cout<<output<<std::endl;
		Json::Value root;
		if(!toJsonValue(output,root))
		{
			return false;
		}
		Json::Value data;
		if(!root.removeMember("data",&data))
		{
			return false;
		}
		if(data.type() != Json::arrayValue)
		{
			return false;
		}	
		for(auto it = data.begin(); it != data.end(); it++)
		{
			SNetworkConfig snc;
			// snc.type = (*it)["type"].asString()=="ether"?SNetworkConfig::EType::eEthernet:SNetworkConfig::EType::eLocalLoopback;
			switch((*it)["type"].asUInt64())
			{
				case 1:
					snc.type = SNetworkConfig::EType::eEhernet;
					break;
				case 2:
					// 注释掉 WLAN 类型设置
					// snc.type = SNetworkConfig::EType::eWLan;
					snc.type = SNetworkConfig::EType::eUnKnow;
					break;
				case 512:
					snc.type = SNetworkConfig::EType::ePPP;
					break;
				case 772:
					snc.type = SNetworkConfig::EType::eLocalLoopback;
					break;
				default:
					snc.type = SNetworkConfig::EType::eUnKnow;
					break;
			}
			snc.name = (*it)["name"].asString();
			snc.ipv4 = (*it)["ipv4"].asString();
			snc.netmask = (*it)["netmask"].asString();
			snc.mac = (*it)["mac"].asString();
			snc.ipv6 = (*it)["ipv6"].asString();
			snc.dns = (*it)["dns"].asString();
			snc.gateway = (*it)["gateway"].asString();
			snc.rxPackets = (*it)["rxPackets"].asUInt64();
			snc.rxBytes = (*it)["rxBytes"].asUInt64();
			snc.txPackets = (*it)["txPackets"].asUInt64();
			snc.txBytes = (*it)["txBytes"].asUInt64();
			snc.linkDetected = (*it)["linkDetected"].asString() == "yes" ? true:false;
			snc.dhcp = (*it)["dhcp"].asString() == "yes" ? true : false;
			snc.enableChange = (*it)["enableChange"].asString() == "yes" ? true : false;
			snc.ssid = (*it)["ssid"].asString();
			networkConfigList.push_back(snc);
		}
#endif
	}
	return true;
}

bool CSystemSetting::getMemoryInfo(SMemoryInfo& memoryInfo)
{
	bool (*getMemoryInfo)(void*) = nullptr;
	dynLibs_->getFunctionPointer("getMemoryInfo", "getMemoryInfo", (void*&)getMemoryInfo);
	if (getMemoryInfo)
	{
		return getMemoryInfo((void*)&memoryInfo);
	}
	else
	{
#ifdef _WIN32
		// Windows platform uses API to get memory information
		return GetWindowsMemoryInfo(memoryInfo);
#else
		// Linux platform uses shell scripts
		std::string output;
		std::string args;
		shell_->execute("getMemoryInfo", args, output);
		//std::cout<<output<<std::endl;
		Json::Value root;
		if(!toJsonValue(output,root))
		{
			return false;
		}
		Json::Value jv;
		if(root.removeMember("memTotal",&jv))
		{
			memoryInfo.memTotal = jv.asUInt64();
		}
		if(root.removeMember("memUsed",&jv))
		{
			memoryInfo.memUsed = jv.asUInt64();
		}
		if(root.removeMember("memFree",&jv))
		{
			memoryInfo.memFree = jv.asUInt64();
		}
		if(root.removeMember("swapTotal",&jv))
		{
			memoryInfo.swapTotal = jv.asUInt64();
		}
		if(root.removeMember("swapUsed",&jv))
		{
			memoryInfo.swapUsed = jv.asUInt64();
		}
		if(root.removeMember("swapFree",&jv))
		{
			memoryInfo.swapFree = jv.asUInt64();
		}
#endif
	}
	return true;
}

bool CSystemSetting::getStorageInfo(const std::string& name, SStorageInfo& storageInfo)
{
	return true;
}

bool CSystemSetting::getStorageInfoList(StorageInfoList& storageInfoList)
{
	bool (*getStorageInfoList)(void*) = nullptr;
	dynLibs_->getFunctionPointer("getStorageInfoList", "getStorageInfoList", (void*&)getStorageInfoList);
	if (getStorageInfoList)
	{
		return getStorageInfoList((void*)&storageInfoList);
	}
	else
	{
#ifdef _WIN32
		// Windows平台使用API获取存储信息
		return GetWindowsStorageInfo(storageInfoList);
#else
		// Linux平台使用shell脚本
		std::string output;
		std::string args;
		shell_->execute("getStorageInfoList", args, output);
		Json::Value root, jv;
		if (!toJsonValue(output, root))
		{
			return false;
		}
		for (auto& it : root["data"])
		{
			SStorageInfo sif;
			if (it.removeMember("name", &jv))
				sif.name = jv.asString();
			if (it.removeMember("capacity", &jv))
				sif.capacity = jv.asUInt64();
			if (it.removeMember("used", &jv))
				sif.used = jv.asUInt64();
			if (it.removeMember("free", &jv))
				sif.free = jv.asUInt64();
			storageInfoList.push_back(sif);
		}
#endif
	}
	return true;
}

bool CSystemSetting::getProcessInfo(const std::string& name, SProcessInfo& processInfo)
{
	return true;
}

bool CSystemSetting::getProcessInfoList(ProcessInfoList& processInfoList)
{
#ifdef _WIN32
	// 在Windows Server上不执行
	if (IsWindowsServer()) {
		return false;
	}
	// Windows平台使用API获取进程信息
	return GetWindowsProcessInfo(processInfoList);
#else
	// Linux平台暂未实现
	return true;
#endif
}

bool CSystemSetting::getAvailableWirelessList(const std::string& args, WirelessInfoList& wirelessInfoList)
{
	bool (*getAvailableWirelessList)(void*) = nullptr;
	dynLibs_->getFunctionPointer("getAvailableWirelessList", "getAvailableWirelessList", (void*&)getAvailableWirelessList);
	if (getAvailableWirelessList)
	{
		return getAvailableWirelessList((void*)&wirelessInfoList);
	}
	else
	{
#ifdef _WIN32
		// 在Windows Server上不执行
		if (IsWindowsServer()) {
			return false;
		}
		// Windows平台使用API获取无线网络列表
		return GetWindowsWirelessList(wirelessInfoList);
#else
		// Linux平台使用shell脚本
		std::string output;
		shell_->execute("getAvailableWirelessList", args, output);
		Json::Value root, jv;
		if (!toJsonValue(output, root))
		{
			return false;
		}
		for (auto& it : root["data"])
		{
			SWirelessInfo wlf;
			if (it.removeMember("ssid", &jv))
				wlf.ssid = jv.asString();
			if (it.removeMember("signal", &jv))
				wlf.signal = jv.asInt();
			if (it.removeMember("flags", &jv))
				wlf.flags = jv.asString();
			wirelessInfoList.push_back(wlf);
		}
#endif
	}
	return true;
}

bool CSystemSetting::getSavedWirelessList(WirelessInfoList& wirelessInfoList)
{
	return true;
}

bool CSystemSetting::getRouteInfoList(RouteInfoList& routeInfoList)
{
	return true;
}

bool CSystemSetting::getCPUInfo(SCPUInfo& cpuInfo)
{
	bool (*getCPUInfo)(SCPUInfo *cpuInfo) = nullptr;
	dynLibs_->getFunctionPointer("getCPUInfo", "getCPUInfo", (void*&)getCPUInfo);
	if (getCPUInfo)
	{
		getCPUInfo(&cpuInfo);
	}
	else
	{
#ifdef _WIN32
		// Windows平台使用API获取CPU信息
		return GetWindowsCPUInfo(cpuInfo);
#else
		// Linux平台使用shell脚本
		std::string output;
		std::string args;
		shell_->execute("getCPUInfo", args, output);
		//std::cout << __func__ << std::endl << output << std::endl;
		Json::Value root;
		if(!toJsonValue(output,root))
		{
			return false;
		}
		Json::Value jv;
		if(root.removeMember("usage",&jv))
		{
			if(jv.isString())
			{
				cpuInfo.usage = atof(jv.asString().c_str());
			}
			else if(jv.isDouble())
			{
				cpuInfo.usage = jv.asDouble();
			}
		}
#endif
	}
	return true;
}

bool CSystemSetting::getCurrentStorageInfo(SStorageInfo& storageInfo)
{
	bool (*getCurrentStorageInfo)(SStorageInfo* storageInfo) = nullptr;
	dynLibs_->getFunctionPointer("getCurrentStorageInfo","getCurrentStorageInfo",(void*&)getCurrentStorageInfo);
	if(getCurrentStorageInfo)
	{
		getCurrentStorageInfo(&storageInfo);
	}
	else
	{
#ifdef _WIN32
		// Windows平台获取当前目录所在驱动器信息
		char currentPath[MAX_PATH];
		if (GetCurrentDirectoryA(MAX_PATH, currentPath)) {
			char drivePath[4] = {currentPath[0], ':', '\\', '\0'};
			ULARGE_INTEGER freeBytesAvailable, totalNumberOfBytes, totalNumberOfFreeBytes;
			if (GetDiskFreeSpaceExA(drivePath, &freeBytesAvailable, &totalNumberOfBytes, &totalNumberOfFreeBytes)) {
				storageInfo.name = drivePath;
				storageInfo.capacity = totalNumberOfBytes.QuadPart / 1024; // 转换为KB
				storageInfo.free = totalNumberOfFreeBytes.QuadPart / 1024;
				storageInfo.used = storageInfo.capacity - storageInfo.free;
			}
		}
#else
		// Linux平台使用shell脚本
		std::string output;
		std::string args;
		shell_->execute("getCurrentStorageInfo", args, output);

		//std::cout << __func__ << std::endl << output << std::endl;
		
		Json::Value root;
		if(!toJsonValue(output,root))
		{
			return false;
		}
		Json::Value jv;
		if(root.removeMember("name",&jv))
		{
			storageInfo.name = jv.asString();
		}
		if(root.removeMember("capacity",&jv))
		{
			storageInfo.capacity = jv.asUInt64();
		}
		if(root.removeMember("used",&jv))
		{
			storageInfo.used = jv.asUInt64();
		}
		if(root.removeMember("free",&jv))
		{
			storageInfo.free = jv.asUInt64();
		}
#endif
	}
	return true;
}

bool CSystemSetting::getProductNumber(std::string& pn)
{
#ifdef _WIN32
	// 在Windows Server上不执行
	if (IsWindowsServer()) {
		return false;
	}
	// Windows平台获取产品序列号
	HKEY hKey;
	if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
		char buffer[256];
		DWORD bufferSize = sizeof(buffer);
		if (RegQueryValueExA(hKey, "ProductId", NULL, NULL, (LPBYTE)buffer, &bufferSize) == ERROR_SUCCESS) {
			pn = buffer;
			RegCloseKey(hKey);
			return true;
		}
		RegCloseKey(hKey);
	}
	return false;
#else
	// Linux平台暂未实现
	return false;
#endif
}

bool CSystemSetting::setNetWorkConfig(const std::string &args)
{
	bool (*setNetWorkConfig)(void) = nullptr;
	dynLibs_->getFunctionPointer("setNetWorkConfig", "setNetWorkConfig", (void*&)setNetWorkConfig);
	if (setNetWorkConfig)
	{
		return setNetWorkConfig();
	}
	else
	{
#ifdef _WIN32
		// 在Windows Server上不执行
		if (IsWindowsServer()) {
			return false;
		}
		// Windows平台网络配置需要解析args参数并使用netsh命令
		// 这里提供基本实现，实际使用需要根据具体参数格式调整
		std::string cmd = "netsh interface ip set address " + args;
		int result = system(cmd.c_str());
		return (result == 0);
#else
		// Linux平台使用shell脚本
		std::string output;
		bool ret = shell_->execute("setNetWorkConfig", args, output);
		return ret;
#endif
	}
}

bool CSystemSetting::setWirelessConnect(const std::string& args)
{
	bool (*setWirelessConnect)(void) = nullptr;
	dynLibs_->getFunctionPointer("setWirelessConnect", "setWirelessConnect", (void*&)setWirelessConnect);
	if (setWirelessConnect)
	{
		return setWirelessConnect();
	}
	else
	{
#ifdef _WIN32
		// 在Windows Server上不执行
		if (IsWindowsServer()) {
			return false;
		}
		// 注释掉 Windows 平台无线连接，禁用 netsh wlan connect 命令
		// std::string cmd = "netsh wlan connect " + args;
		// int result = system(cmd.c_str());
		// return (result == 0);
		return false;  // 直接返回失败
#else
		// Linux平台使用shell脚本
		std::string output;
		bool ret = shell_->execute("setWirelessConnect", args, output);
		return ret;
#endif
	}
}

bool CSystemSetting::setWirelessConfig(const std::string& args)
{
	bool (*setWirelessConfig)(void) = nullptr;
	dynLibs_->getFunctionPointer("setWirelessConfig", "setWirelessConfig", (void*&)setWirelessConfig);
	if (setWirelessConfig)
	{
		return setWirelessConfig();
	}
	else
	{
		std::string output;
		bool ret = shell_->execute("setWirelessConfig", args, output);
		return ret;
	}
}

bool CSystemSetting::setWirelessDisconnect(const std::string& args)
{
	bool (*setWirelessDisconnect)(void) = nullptr;
	dynLibs_->getFunctionPointer("setWirelessDisconnect", "setWirelessDisconnect", (void*&)setWirelessDisconnect);
	if (setWirelessDisconnect)
	{
		return setWirelessDisconnect();
	}
	else
	{
		std::string output;
		bool ret = shell_->execute("setWirelessDisconnect", args, output);
		return ret;
	}
}

bool CSystemSetting::setDnsConfig(const std::string& args)
{
	bool (*setDnsConfig)(void) = nullptr;
	dynLibs_->getFunctionPointer("setDnsConfig", "setDnsConfig", (void*&)setDnsConfig);
	if (setDnsConfig)
	{
		return setDnsConfig();
	}
	else
	{
#ifdef _WIN32
		// 在Windows Server上不执行
		if (IsWindowsServer()) {
			return false;
		}
		// Windows平台DNS配置，使用netsh命令
		std::string cmd = "netsh interface ip set dns " + args;
		int result = system(cmd.c_str());
		return (result == 0);
#else
		// Linux平台使用shell脚本
		std::string output;
		bool ret = shell_->execute("setDnsConfig", args, output);
		return ret;
#endif
	}
}

bool CSystemSetting::getSyncTime(STimeSyncInfo& timeSyncInfo)
{
	bool (*getSyncTime)(void) = nullptr;
	dynLibs_->getFunctionPointer("getSyncTime", "getSyncTime", (void*&)getSyncTime);
	if (getSyncTime)
	{
		return getSyncTime();
	}
	else
	{
#ifdef _WIN32
		// 在Windows Server上不执行
		if (IsWindowsServer()) {
			return false;
		}
		// Windows平台获取时间同步信息
		// 通过注册表获取NTP服务器配置
		HKEY hKey;
		if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SYSTEM\\CurrentControlSet\\Services\\w32time\\Parameters", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
			char buffer[256];
			DWORD bufferSize = sizeof(buffer);
			if (RegQueryValueExA(hKey, "NtpServer", NULL, NULL, (LPBYTE)buffer, &bufferSize) == ERROR_SUCCESS) {
				timeSyncInfo.address = buffer;
			}
			RegCloseKey(hKey);
		}
		
		// 检查W32Time服务状态
		SC_HANDLE scManager = OpenSCManagerA(NULL, NULL, SC_MANAGER_CONNECT);
		if (scManager) {
			SC_HANDLE service = OpenServiceA(scManager, "W32Time", SERVICE_QUERY_STATUS);
			if (service) {
				SERVICE_STATUS status;
				if (QueryServiceStatus(service, &status)) {
					timeSyncInfo.Switch = (status.dwCurrentState == SERVICE_RUNNING);
				}
				CloseServiceHandle(service);
			}
			CloseServiceHandle(scManager);
		}
		
		return true;
#else
		// Linux平台使用shell脚本
		std::string output;
		std::string args;
		bool ret = shell_->execute("getSyncTime", args, output);

		//std::cout << __func__ << output << std::endl;
		
		Json::Value root;
		if (!toJsonValue(output, root))
		{
			return false;
		}
		Json::Value jv;
		if (root.removeMember("address", &jv))
		{
			timeSyncInfo.address = jv.asString();
		}
		if (root.removeMember("switch", &jv))
		{
			timeSyncInfo.Switch = jv.asBool();
		}

		return true;
#endif
	}
}

bool CSystemSetting::setSyncTime(const std::string& args)
{
	bool (*setSyncTime)(void) = nullptr;
	dynLibs_->getFunctionPointer("setSyncTime", "setSyncTime", (void*&)setSyncTime);
	if (setSyncTime)
	{
		return setSyncTime();
	}
	else
	{
#ifdef _WIN32
		// 在Windows Server上不执行
		if (IsWindowsServer()) {
			return false;
		}
		// Windows平台设置时间同步
		// 使用w32tm命令配置NTP服务器
		std::string cmd = "w32tm /config /manualpeerlist:\"" + args + "\" /syncfromflags:manual";
		int result = system(cmd.c_str());
		if (result == 0) {
			// 重启W32Time服务
			system("net stop w32time");
			system("net start w32time");
		}
		return (result == 0);
#else
		// Linux平台使用shell脚本
		std::string output;
		bool ret = shell_->execute("setSyncTime", args, output);
		return ret;
#endif
	}
}

bool CSystemSetting::doSyncTime(const std::string& args)
{
	bool (*doSyncTime)(void) = nullptr;
	dynLibs_->getFunctionPointer("doSyncTime", "doSyncTime", (void*&)doSyncTime);
	if (doSyncTime)
	{
		return doSyncTime();
	}
	else
	{
#ifdef _WIN32
		// 在Windows Server上不执行
		if (IsWindowsServer()) {
			return false;
		}
		// Windows平台执行时间同步
		std::string cmd = "w32tm /resync";
		int result = system(cmd.c_str());
		return (result == 0);
#else
		// Linux平台使用shell脚本
		std::string output;
		bool ret = shell_->execute("doSyncTime", args, output);
		return ret;
#endif
	}
}

extern "C"
{
	SYSTEM_SETTING_API ISystemSetting* create()
	{
		return new CSystemSetting;
	}

	SYSTEM_SETTING_API void destory(ISystemSetting* p)
	{
		if (p) delete p;
	}
}

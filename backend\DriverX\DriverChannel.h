#pragma once

#include <string>
#include <atomic>
#include <functional>
#include <memory>
#include <future>
#include <map>
#include <unordered_map>
#include <future>
#include <queue>
#include <set>

#include "spdlog/spdlog.h"
#include "spdlog/sinks/rotating_file_sink.h"
#include "spdlog/sinks/daily_block_file_sink.h"
#include "spdlog/sinks/daily_block_level_file_sink.h"

#include "IDriver.h"

#include "LogCaching.h"

#include "json/json.h"

#define POINT_NAME_SCANOFF		"_scanoff"
#define	POINT_NAME_INHIBIT		"_inhibit"

namespace DRIVER
{
	class IChannelCallback
	{
	public:
		IChannelCallback() {}
		virtual ~IChannelCallback() {}

		virtual void onChannelStatus(const std::string& channelName, const SStatus& status) = 0;

		virtual void onChannelOnline(const std::string& channelName, bool online) = 0;

		virtual void onChannelData(const std::string& channelName, ChannelDataSetSptr dataSetSptr) = 0;

		virtual void onChannelHistoryData(const std::string& channelName, ChannelHistoryDataSetSptr dataSetSptr) = 0;

		virtual void onChannelEvent(const std::string& channelName, const std::string& deviceName, SEventSptr eventSptr) = 0;

		virtual void onChannelControlFeedback(const std::string& channelName, const SControlFeedback& feedback) = 0;
		
		//update devices allpoint offline
		virtual void onChannelUpdateDeviceOffline(const std::string& channelName, bool isOffline) = 0;

		virtual void onChannelUpdateDeviceScanoff(const std::string& channelName, std::string& deviceName, bool scanoff) = 0;

		virtual void onChannelUpdateDeviceInhibit(const std::string& channelName, std::string& deviceName, bool inhibit) = 0;
	};

	struct SPointConfig
	{
		std::string name;
		std::string pointType; //temp
		std::string type; //temp
		std::string alias; //temp
		std::string description;
		bool readOnly;
		bool online;
		float minValue;
		float maxValue;
		float added;
		float coefficient;
		std::map<std::string, std::string> value;
		OriginProtocol pointProtocol;
		int pos; //ԭ���еĵ�λ˳��
		SPointConfig() :
			readOnly(false),
			online(true),
			minValue(0.0),
			maxValue(0.0),
			added(0.0),
			coefficient(1.0),
			pos(0)
			{}
	};

	struct SDeviceConfig
	{
		std::string name;
		std::string alias; //temp
		bool online;
		OriginProtocol deviceProtocol;
		std::map<std::string, SPointConfig> pointTable;
		std::map<std::string, SPointConfig> innerPointTable;
		SDeviceConfig():
			online(true) {}
	};

	struct SChannelConfig
	{
		std::string name;	//id
		std::string alias; //temp	name
		std::string className;
		std::string driverType;
		std::string argumentStr;   //json�ַ�������ǰ��չʾ����ʹ��
		std::string connectionStr; //json�ַ�������ǰ��չʾ����ʹ��
		int logLevel; //TRACE 0 DEBUG 1 INFO 2 WARN 3 ERROR 4 CRITICAL 5 PACKET 6 OFF 7
		int logSize; // MB
		int logBaseSize;
		int logCount;
		bool enableUpdateStatusByDriver;
		bool enableUpdateDataOnChanged;
		bool enableStatus; //ʹ��״̬�����õ㣬ƽ̨��8899�����Ը�
		int64_t regularlyReportInterval;
		uint64_t pollCycle;
		OriginProtocol channelProtocol;
		std::map<std::string, SDeviceConfig> deviceTable;

		std::map<std::string, std::string> multiGatewatConfig;

		std::string localConfigLinkName;
		std::string localConfigLinkId;

		SChannelConfig() :
			logLevel(0),
			logSize(10),
			logBaseSize(1024 * 1024),
			logCount(5),
			enableUpdateStatusByDriver(false),
			enableUpdateDataOnChanged(true),
			enableStatus(true),
			pollCycle(1000),
			regularlyReportInterval(0) {}
	};

	struct SPointInfo;
	struct SDeviceInfo;

	struct SChannelInfo
	{
		std::string name;
		std::string alias;
		std::string driverType;
		uint64_t pollCycle;
		bool enableUpdateStatusByDriver;
		bool enableUpdateDataOnChanged;
		int64_t regularlyReportInterval;
		IProtocol* protocol;

		std::unordered_map<std::string, std::shared_ptr<SDeviceInfo>> deviceMap;
		SChannelInfo() : pollCycle(1000), enableUpdateStatusByDriver(false), enableUpdateDataOnChanged(true), protocol(nullptr), regularlyReportInterval(0) {}
	};

	struct SDeviceInfo
	{
		enum EStatus
		{
			eFree,
			eTLSM,
			eKZYZ,
			eJDGP,
			eWXGP
		};

		SChannelInfo& channelInfo;
		std::string name;
		bool online;
		EStatus status;
		IProtocol* protocol;
		std::unordered_map<std::string, std::shared_ptr<SPointInfo>> pointMap;
		

		bool _online;	//status
		bool _scanoff;	//control
		bool _inhibit;	//control

		SDeviceInfo(SChannelInfo& channelInfo) : 
			channelInfo(channelInfo), online(true), protocol(nullptr),
			_online(false), _scanoff(false), _inhibit(false){}
	};

	struct SPointInfo
	{
		SDeviceInfo& deviceInfo;
		std::string name;
		std::string type; // AI,DI,EI...
		std::string pointType;	//state control stateControl
		bool readOnly;
		bool online;
		bool enableAC;
		float minValue;
		float maxValue;
		float added;
		float coefficient;
		std::map<std::string, std::string> value;

		IProtocol* protocol;

		SPointInfo(SDeviceInfo& deviceInfo) : 
			deviceInfo(deviceInfo),
			readOnly(false),
			online(true),
			enableAC(false),
			minValue(0.0),
			maxValue(0.0),
			added(0.0),
			coefficient(1.0),
			protocol(nullptr) {}
	};

	struct SDeviceAndPointName
	{
		std::string deviceName;
		std::string pointName;
	};

	struct SDataTrafficStatistics
	{
		uint64_t recv;
		uint64_t send;

		SDataTrafficStatistics() :recv(0), send(0) {}

		static std::string formatBytes(size_t bytes) 
		{
			const char* units[] = { "B", "KB", "MB", "GB", "TB" };
			int i = 0;
			double size = static_cast<double>(bytes);
			while (size >= 1024.0 && i < 4) {
				size /= 1024.0;
				i++;
			}
			char buffer[100];
			if (i == 0) {
				snprintf(buffer, sizeof(buffer), "%.0f", size);
			}
			else {
				snprintf(buffer, sizeof(buffer), "%.2f", size);
			}
			return std::string(buffer) + units[i];
		}

		std::string toString()
		{
			Json::Value root;
			root["recv"] = formatBytes(recv);
			root["send"] = formatBytes(send);
			return root.toStyledString();
		}
	};

	class CDriverChannel;
	using  CDriverChannelSptr = std::shared_ptr<CDriverChannel>;

	class CDriverChannel : public IDriverCallback
	{
		// control info
		using ControlTask = std::function<void()>;
		std::mutex controlTaskQueueMutex_;
		std::queue<ControlTask> controlTaskQueue_;

	private:
		IDriverSptr driver_;
		std::unordered_map<std::string, SData> key2PointDataMap_;
		std::unordered_map<std::string, std::vector<std::shared_ptr<SPointInfo>>> key2PointMap_;
		std::unordered_map<std::string, SValue> pointArtificialValueMap_;

		//open record
		std::map<int64_t, bool> openRecordMap_;
		void recordOpen(bool openFlag);
		bool isFirstOpen_;

		bool openFlag_;
		bool updateStatusByDriver_;
		bool status_;
		bool lastStatus_;
		bool onlineSeparate_; //��·���豸������״̬�Ƿ�ֿ�����
		uint64_t lastRegularlyReportInterval_;
		void updateInnerPoint();

		std::mutex driverMutex_;

		std::shared_ptr<spdlog::logger> logger_;

		//thread
		std::atomic<bool> running_;
		std::thread* loopThread_;
		void loop();

		SChannelInfo channelInfo_;
		SProtocolNode protocolNode_;
		IChannelCallback* cb_;

		ChannelDataSet dataSet_;
		ChannelHistoryDataSet historyDataSet_;

		//log
		CLogCaching *lc_;
		bool insertLog(ELogType type, const std::string& content);

		//packet content count
		SDataTrafficStatistics dataTrafficCount_;

		bool hasUpdataDeviceOffline;

	public:
		explicit CDriverChannel(const std::string& channelName, const std::string& driverType);
		~CDriverChannel();

		bool initial(const SChannelConfig& cfg);
		bool uninitial();
		bool start();
		bool stop();
		std::future<SControlFeedback> control(const SControlInfo& controlInfo);
		std::future<SControlFeedback> control(const std::vector<SControlInfo>& controlInfos);
		bool controlSet(const std::unordered_map<std::string, std::unordered_map<SControlInfo::EType, std::vector<SControlInfo>>>& controlSets);
		EStatusCode getBufferedData(ChannelDataSet& dataSet);
		EStatusCode getBufferedData(const std::string& deviceName, DeviceDataSet& dataSet, bool isWeb = false);
		EStatusCode getBufferedData(const std::string& deviceName, const std::string& pointName, SData& data);
		EStatusCode getDeviceOnlineStatus(ChannelDataSet& dataSet);
		EStatusCode getChannelOnline(ChannelDataSet& dataSet);
		EStatusCode setArtificialValue(const SControlInfo& controlInfo);
		EStatusCode setDeviceStatus(const std::string& deviceName);
		EStatusCode setPointReadOnly(const std::string& deviceName, const std::string& pointName);
		EStatusCode setPointWritable(const std::string& deviceName, const std::string& pointName);
		EStatusCode offlinePoint(const SControlInfo& controlInfo);
		EStatusCode onlinePoint(const SControlInfo& controlInfo);
		EStatusCode offlineDevice(const SControlInfo& controlInfo);
		EStatusCode onlineDevice(const SControlInfo& controlInfo);
		void setCallback(IChannelCallback* cb);
		bool hasDevice(const std::string& deviceName);
		bool getStatus();
		int getConnectQuality();
		EStatusCode sendAllData();
		SData calcDataOffset(const std::shared_ptr<SPointInfo>& pointInfo, const SData& originData);

		// IDriverCallback
		virtual void onLog(ELogLevel logLevel, const std::string& log) override;
		virtual void onLog(ELogLevel logLevel, const std::vector<uint8_t>& data) override;
		virtual void onLog(const std::string& func, const std::string& type, const std::string& addr, const std::string& log) override;
		virtual void onStatus(const SStatus& status) override;
		virtual void onOnline(bool online) override;
		virtual void onData(const std::string& key, const SData& data) override;
		virtual void onInnerData(const std::string& deviceName, const std::string& pointName, SData& data) override;
		virtual void onHistoryData(const std::string& key, const SData& data) override;
		virtual void onEvent(const std::string& key, SEvent&& event) override;
		virtual void onEventByDevice(const std::string& deviceID, SEvent&& event) override;
		virtual void onControlFeedback(const SControlFeedback& feedback) override;
		virtual void setOnlineSeparate(bool separate) override;
		virtual void onPacketContentCount(const SPacketContentInfo& info) override;
		virtual void onChannelControlFeedback(const std::string& channelName, const SControlFeedback& feedback) override;
	};

} //namespace DRIVER end
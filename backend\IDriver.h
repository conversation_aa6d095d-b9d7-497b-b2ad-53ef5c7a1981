#pragma once
#ifndef _IDRIVER_H_
#define _IDRIVER_H_

#define DRIVER_DLL_BUILD

#if defined(DRIVER_DLL_BUILD)
#if defined(_MSC_VER) || defined(__MINGW32__)
#define DRIVER_API __declspec(dllexport)
#elif defined(__GNUC__) || defined(__clang__)
#define DRIVER_API __attribute__((visibility("default")))
#endif // if defined(_MSC_VER)

#elif defined(DRIVER_DLL)
#if defined(_MSC_VER) || defined(__MINGW32__)
#define DRIVER_API __declspec(dllimport)
#endif // if defined(_MSC_VER)
#endif // ifdef DRIVER_DLL_BUILD

#include <map>
#include <vector>
#include <string>
#include <memory>
#include <functional>
#include <cassert>
#include <ostream>
#include <math.h>
#include <unordered_map>
#include <string.h>

namespace DRIVER {
	struct IProtocol;
}

struct SValue
{
	enum EValueType
	{
		eValueNull = 0,		///< 'null' value
		eValueInt = 1,		///< int value
		eValueUInt = 2,		///< uint
		eValueReal = 3,     ///< double value
		eValueString = 4,   ///< UTF-8 string value
		eValueBoolean = 5,  ///< bool value
		eValueJson = 6		///< json object, store in str
	};

	SValue() : type(SValue::eValueNull), b(false) {}
	SValue(bool v) : type(SValue::eValueBoolean), b(v) {}
	SValue(int32_t v) : type(SValue::eValueInt), i64(v) {}
	SValue(uint32_t v) : type(SValue::eValueUInt), i64(v) {}
	SValue(int64_t v) : type(SValue::eValueInt), i64(v) {}
	SValue(uint64_t v) : type(SValue::eValueUInt), i64(v) {}
	SValue(float v) : type(SValue::eValueReal), f64(v) {}
	SValue(double v) : type(SValue::eValueReal), f64(v) {}
	SValue(const std::string& v) : type(SValue::eValueString), str(v) {}
	SValue(std::string&& v) : type(SValue::eValueString), str(v) {}
	SValue(const char* v) : type(SValue::eValueString), str(v) {}
	SValue(const SValue& sv) : type(SValue::eValueNull), b(false)
	{
		type = sv.type;
		switch (sv.type)
		{
		case SValue::eValueNull:
			break;
		case SValue::eValueInt:
		case SValue::eValueUInt:
			i64 = sv.i64;
			break;
		case SValue::eValueReal:
			f64 = sv.f64;
			break;
		case SValue::eValueString:
			new(&str) std::string(sv.str);
			break;
		case SValue::eValueBoolean:
			b = sv.b;
			break;
		case SValue::eValueJson:
			new(&str) std::string(sv.str);
			break;
		default:
			break;
		}
	}

	~SValue() { if (type == SValue::eValueString || type == SValue::eValueJson) str.~basic_string(); }

	EValueType getType() const { return type; }
	void setType(SValue::EValueType _type) { type = _type; }

	void setValue(bool v) { b = v; type = SValue::eValueBoolean; }
	void setValue(int32_t v) { i64 = v; type = SValue::eValueInt; }
	void setValue(uint32_t v) { i64 = v; type = SValue::eValueUInt; }
	void setValue(int64_t v) { i64 = v; type = SValue::eValueInt; }
	void setValue(uint64_t v) { i64 = v; type = SValue::eValueUInt; }
	void setValue(float v) { f64 = v; type = SValue::eValueReal; }
	void setValue(double v) { f64 = v; type = SValue::eValueReal; }
	void setValue(const std::string& v) {
		new(&str) std::string(v); type = SValue::eValueString;
	}
	void setValue(std::string&& v) {
		new(&str) std::string(v); type = SValue::eValueString;
	}
	void setValue(const char* v) {
		new(&str) std::string(v); type = SValue::eValueString;
	}
	void setValue(const SValue& v) {
		if (type == SValue::eValueString || type == SValue::eValueJson) {
			type = eValueNull;
			str.~basic_string();
		}

		type = v.type;
		switch (v.type)
		{
		case SValue::eValueNull:
			break;
		case SValue::eValueInt:
		case SValue::eValueUInt:
			i64 = v.i64;
			break;
		case SValue::eValueReal:
			f64 = v.f64;
			break;
		case SValue::eValueString:
			new(&str) std::string(v.str);
			break;
		case SValue::eValueBoolean:
			b = v.b;
			break;
		case SValue::eValueJson:
			new(&str) std::string(v.str);
			break;
		default:
			break;
		}
	}

	template <typename T> SValue& operator = (T v) { 
		if (type == SValue::eValueString || type == SValue::eValueJson) {
			type = eValueNull;
			str.~basic_string();
		}
		setValue(v);
		return *this;
	};

	SValue& operator = (const SValue& v) {
		if (type == SValue::eValueString || type == SValue::eValueJson) {
			type = eValueNull;
			str.~basic_string();
		}
		setValue(v);
		return *this;
	};

	template <typename T = int32_t> T asInt() const {
		assert(type == eValueInt || type == eValueUInt);
		return T(i64);
	};

	template <typename T = float> T asFloat() const {
		assert(type == eValueReal);
		return T(f64);
	};

	std::string asString() const {
		assert(type == eValueString);
		return str;
	}

	bool asBool() const {
		assert(type == eValueBoolean);
		return b;
	}

	template <typename T = int32_t> T toInt() const {
		T rv;
		switch (type)
		{
		case SValue::eValueInt:
		case SValue::eValueUInt:
			rv = T(i64);
			break;
		case SValue::eValueReal:
			rv = T(f64);
			break;
		case SValue::eValueString:
			rv = T(atoi(str.c_str()));
			break;
		case SValue::eValueBoolean:
			rv = T(b);
			break;
		default:
			break;
		}
		return rv;
	};

	template <typename T = float> T toFloat() const {
		T rv;
		switch (type)
		{
		case SValue::eValueInt:
		case SValue::eValueUInt:
			rv = T(i64);
			break;
		case SValue::eValueReal:
			rv = T(f64);
			break;
		case SValue::eValueString:
			rv = T(atof(str.c_str()));
			break;
		case SValue::eValueBoolean:
			rv = T(b);
			break;
		default:
			break;
		}
		return rv;
	};

	std::string toString() const {
		std::string ret;
		switch (type)
		{
		case SValue::eValueNull:
			break;
		case SValue::eValueInt:
		case SValue::eValueUInt:
			ret = std::to_string(i64);
			break;
		case SValue::eValueReal:
			ret = std::to_string(f64);
			break;
		case SValue::eValueString:
			ret = str;
			break;
		case SValue::eValueBoolean:
			ret = b ? "true" : "false";
			break;
		case SValue::eValueJson:
			ret = str;
			break;
		default:
			break;
		}
		return ret;
	}

	bool toBool() const {
		bool rv;
		switch (type)
		{
		case SValue::eValueInt:
		case SValue::eValueUInt:
			rv = bool(i64);
			break;
		case SValue::eValueReal:
			rv = bool(f64);
			break;
		case SValue::eValueString:
			rv = str == "true";
			break;
		case SValue::eValueBoolean:
			rv = b;
			break;
		default:
			break;
		}
		return rv;
	}

	void setJson(const std::string& v) {
		if (type == SValue::eValueString || type == SValue::eValueJson) str.~basic_string();
		type = SValue::eValueJson;
		new(&str) std::string(v); 
	}

	bool isRealValid() const 
	{
		int ret = fpclassify(f64);
		switch (ret) 
		{
			case FP_NAN:      // ������
			case FP_INFINITE: // �����
			case FP_SUBNORMAL:// �����������ǳ��ӽ�0������
				return false;
			case FP_ZERO:     // ��
				return true;  // 0����Ч������
			case FP_NORMAL:   // ������
				if (fabs(f64) < 1.0e-30)
					return false;
				return true;
			default:
				return false;
		}
	}

	bool operator ==(const SValue& v)
	{
		if (type != v.type)
		{
			return false;
		}

		bool ret = false;
		switch (type)
		{
		case SValue::eValueInt:
		case SValue::eValueUInt:
			ret = i64 == v.i64;
			break;
		case SValue::eValueReal:
			//ret = std::abs(f64 - v.f64) < 0.0000001;
			ret = memcmp(&f64, &v.f64, sizeof(double)) == 0;
			break;
		case SValue::eValueString:
			ret = str == v.str;
			break;
		case SValue::eValueBoolean:
			ret = b == v.b;
			break;
		case SValue::eValueJson:
			ret = str == v.str;
			break;
		default:
			break;
		}
		return ret;
	}

	private:
		EValueType type;
		union
		{
			int64_t i64;
			double f64;
			std::string str;
			bool b;
		};
};

struct SControlInfo
{
	enum EType
	{
		eTLSM,
		eKZYZ,
		eJDGP,
		eWXGP,
		eOfflinePoint,
		eOnlinePoint,
		emanset,
		eWrite
	};
	EType controlType;
	std::string sourceID;
	int sequence; 
	std::string channelName;
	std::string deviceName;
	std::string pointName;
	SValue value;
	std::string raw;
};

struct SControlSetInfo :SControlInfo
{
	const DRIVER::IProtocol* const channelProtocol;
	const DRIVER::IProtocol* const deviceProtocol;
	const DRIVER::IProtocol* const pointProtocol;

	SControlSetInfo(SControlInfo& info, const DRIVER::IProtocol* ch, const DRIVER::IProtocol* dev, const DRIVER::IProtocol* pt)
		: channelProtocol(ch), deviceProtocol(dev), pointProtocol(pt)
	{
		controlType = info.controlType;
		sourceID = info.sourceID;
		sequence = info.sequence;
		deviceName = info.deviceName;
		pointName = info.pointName;
		value = info.value;
	}
};


struct SControlFeedback
{
	enum EStatus
	{
		eSuccess,
		eFailed,
		eError,
		eFinished
	};

	std::string sourceID;
	int sequence;
	std::string deviceName;
	std::string pointName;
	int step = 0;
	EStatus status;
	std::string info;
	std::string raw;
};

// event
struct SEvent
{
	int64_t timestamp;
	std::string id;
	std::string type;
	std::map<std::string, std::vector<uint8_t>> files;
	std::string raw;
	std::map<std::string, SValue> extends;
};

using SEventSptr = std::shared_ptr<SEvent>;

namespace DRIVER
{
	enum EStatusCode
	{
		eStatusSuccess,				///< all success
		eStatusFail,				///< all fail
		eStatusDeviceNotFound,
		eStatusPointNotFound,
		eStatusError,
		eStatusTimeout,
		eStatusNeedReopen,
		eStatusOffline,
		eStatusDoNotSleep,
		eStatusControlAsync,
		eStatusDoNotResponse
	};

	enum EQualityCode
	{
		none = -1,
		invalid = 0,
		unknown = 1,
		alarmst = 2,
		alarmh = 3,
		alarmhh = 4,
		alarml = 5,
		alarmll = 6,
		scanoff = 7,
		bad = 8,
		typeerror = 9,
		outofrange = 10,
		deviceoffline = 11
	};

	//struct SDataKey
	//{
	//	enum EType
	//	{
	//		eTypeInvalid = 0,
	//		eTypeString = 1,
	//		eTypeLayer = 2,	
	//		eTypeAddress = 3
	//	};

	//	struct SLayer
	//	{
	//		std::string deviceID;
	//		std::string pointID;
	//		SLayer(){}
	//		SLayer(const std::string& deviceID, const std::string& pointID) : deviceID(deviceID), pointID(pointID) {}
	//	};

	//	SDataKey():type(eTypeInvalid){}
	//	SDataKey(const std::string& key) :type(eTypeString)
	//	{
	//		new(&stringKey) std::string(key);
	//	}

	//	SDataKey(IProtocol* pointProtocol) :type(eTypeAddress)
	//	{
	//		address = pointProtocol;
	//	}

	//	SDataKey(const std::string& deviceID, const std::string& pointID) :type(eTypeLayer)
	//	{
	//		new(&layer) SLayer(deviceID, pointID);
	//	}

	//	SDataKey& operator = (const SDataKey& v) {
	//		
	//		return *this;
	//	};

	//	bool operator ==(const SDataKey& v)
	//	{
	//		bool rv = false;
	//		if (type != v.type)
	//		{
	//			return rv;
	//		}

	//		switch (type)
	//		{
	//		case eTypeString:
	//			rv = stringKey == v.stringKey;
	//			break;
	//		case eTypeLayer:
	//			rv = layer.deviceID == v.layer.deviceID && layer.pointID == v.layer.pointID;
	//			break;
	//		case eTypeAddress:
	//			rv = address == v.address;
	//			break;
	//		default:
	//			break;
	//		}
	//		return rv;
	//	}

	//	EType getType() const { return type; }

	//private:
	//	EType type;
	//	union
	//	{
	//		std::string stringKey;
	//		SLayer layer;
	//		IProtocol* address;
	//	};
	//};

	struct SData
	{
		int quality;
		uint64_t timestamp;
		SValue value;
		std::vector<EQualityCode> qualityCodeVec;

		SData() :
			quality(192), 
			timestamp(0) {}

		SData& operator = (const SData& other)
		{
			this->quality = other.quality;
			this->timestamp = other.timestamp;
			this->value = other.value;
			this->qualityCodeVec = other.qualityCodeVec;
			return *this;
		}

		bool operator == (const SData& other)
		{
			return value == other.value && quality == other.quality && timestamp == other.timestamp;
		}
	};
	//							    point name
	using DeviceDataSet = std::map<std::string, SData>;
	using DeviceDataSetSptr = std::shared_ptr<DeviceDataSet>;
	//                             device name
	using ChannelDataSet = std::map<std::string, DeviceDataSet>;
	using ChannelDataSetSptr = std::shared_ptr<ChannelDataSet>;
	//							          point name
	using DeviceHistoryDataSet = std::map<std::string, std::vector<SData>>;
	using DeviceHistoryDataSetSptr = std::shared_ptr<DeviceHistoryDataSet>;
	//                                     device name      
	using ChannelHistoryDataSet = std::map<std::string, DeviceHistoryDataSet>;
	using ChannelHistoryDataSetSptr = std::shared_ptr<ChannelHistoryDataSet>;

	// log
	enum ELogLevel
	{
		eLogTrace,
		eLogDebug,
		eLogInfo,
		eLogWarn,
		eLogError,
		eLogPacket,
		n_levels
	};

	typedef struct {
		ELogLevel level;
		std::string name;
	} LogLevelCodeName;

	static const size_t logLevelDescriptionsSize = 6;
	static const LogLevelCodeName logLevelDescriptions[logLevelDescriptionsSize] = {
		{ELogLevel::eLogTrace,"trace"},
		{ELogLevel::eLogDebug,"debug"},
		{ELogLevel::eLogInfo,"info"},
		{ELogLevel::eLogWarn,"warning"},
		{ELogLevel::eLogError,"error"},
		{ELogLevel::eLogPacket,"packet"}
	};

	inline std::string logLevel2String(ELogLevel logLevel)
	{
		for (int i = 0; i < logLevelDescriptionsSize; i++)
		{
			if (logLevelDescriptions[i].level == logLevel)
				return logLevelDescriptions[i].name;
		}
		return std::string("unknow logLevel");
	}

	// channel status
	struct SStatus
	{
		int connectivity; // 
		std::map<std::string, SValue> extends;

		SStatus() {}
		SStatus(int connectivity) : connectivity(connectivity) {}
	};
	
	class IDriver;
	using IDriverSptr = std::shared_ptr<IDriver>;
	
	// protocol definition start
	enum EProtocolType
	{
		eProtocolLink,
		eProtocolDevice,
		eProtocolPoint,
		eProtocolInnerPoint
	};

	using OriginProtocol = std::map<std::string, std::string>;
	struct IProtocol
	{
		IProtocol() {}
		virtual ~IProtocol() {}
	};

	// protocol tree's structure.
	//		*		channel protocol
	//	  / | \
	//	 *  *  *	device protocol (if exist)
	//  / \
	// *   *...		point protocol
	struct SProtocolNode
	{
		IProtocol* protocol;
		std::vector<SProtocolNode> sub;

		SProtocolNode() : protocol(nullptr){}
		~SProtocolNode() {}
	};
	// protocol definition end

	//PacketContentCount
	struct SPacketContentInfo
	{
		enum EType 
		{
			None,
			Send,
			Recv
		};
		EType type;
		uint64_t size;
	};

	class IDriverCallback
	{
	public:
		IDriverCallback() {}
		virtual ~IDriverCallback() {}

		virtual void onLog(ELogLevel logLevel, const std::string& log) = 0;

		virtual void onLog(ELogLevel logLevel, const std::vector<uint8_t>& data) = 0;

		virtual void onLog(const std::string& func, const std::string& type, const std::string& addr, const std::string& log) = 0;

		virtual void onStatus(const SStatus& status) = 0;

		virtual void onOnline(bool online) = 0;

		virtual void onData(const std::string& key, const SData& data) = 0;

		virtual void onInnerData(const std::string& deviceName, const std::string& pointName, SData& data) = 0;

		virtual void onHistoryData(const std::string& key, const SData& data) = 0;

		virtual void onEvent(const std::string& key, SEvent&& event) = 0;
		
		virtual void onEventByDevice(const std::string& deviceID, SEvent&& event) = 0;

		virtual void onControlFeedback(const SControlFeedback& feedback) = 0;

		virtual void setOnlineSeparate(bool separate) = 0;
	
		virtual void onPacketContentCount(const SPacketContentInfo& info) = 0;

		virtual void onChannelControlFeedback(const std::string& channelName, const SControlFeedback& feedback) = 0;
	};

	enum EFlag
	{
		eFlagNone,
		eFlagInterrupt
	};

	class IDriver
	{
	public:
		IDriver() {}
		virtual ~IDriver() {}

		/**
		*  this method starts collecting data and uploading data
		*
		*  @return true on SUCCESS or false on ERROR
		**/
		virtual bool open(const SProtocolNode& pn) = 0;

		/**
		*  this method stops collecting data and uploading data
		*
		*  @return true on SUCCESS or false on ERROR
		**/
		virtual bool close(const SProtocolNode& pn) = 0;

		/**
		*  this method writes data to device
		*
		*  @param channelProtocol		:channelProtocol contains a set of channel argument that created by interface named "createProtocol"
		*  @param deviceProtocol	:deviceProtocol contains a set of device argument that created by interface named "createProtocol"
		*  @param pointProtocol		:pointProtocol contains a set of point argument that created by interface named "createProtocol"
		*  @param value				:value that was read (when status code eStatusSuccess)
		*
		*  @return status code
		**/
		virtual EStatusCode control(const IProtocol* const channelProtocol, const IProtocol* const deviceProtocol, const IProtocol* const pointProtocol, const SControlInfo& controlInfo) = 0;


		virtual EStatusCode controlSet(const std::unordered_map<std::string, std::vector<SControlSetInfo>>& controlValues) = 0;

		/**
		*  this method sets callback 
		*
		* @param cb :cb is sink callback
		**/
		virtual void setCallback(IDriverCallback* cb) = 0;

		/**
		*  This method is called repeatedly (i.e. in a loop) from within a new thread
		* 
		*  @param pn : pn is root of protocol tree, the struct refrence from SProtocolNode annotation
		* 
		*  @return EStatusCode
		**/
		virtual EStatusCode poll(const SProtocolNode& pn) = 0;

		/**
		*  This method is called to generate point key that is unique in current driver
		* 
		*  @param channelProtocol		:channelProtocol contains a set of channel argument that created by interface named "createProtocol"
		*  @param deviceProtocol	:deviceProtocol contains a set of device argument that created by interface named "createProtocol"
		*  @param pointProtocol		:pointProtocol contains a set of point argument that created by interface named "createProtocol"
		* 
		*  @return point key string 
		**/
		virtual std::string getPointKey(const IProtocol* const channelProtocol, const IProtocol* const deviceProtocol, const IProtocol* const pointProtocol) = 0;

		/**
		*  This method is called to create structured protocol object
		*
		*  @param type				:type is protocol type
		*  @param originProtocol	:originProtocol contains a set of key-value pair argument
		*
		*  @return protocol interface pointer
		**/
		virtual IProtocol* createProtocol(EProtocolType type, OriginProtocol& originProtocol) = 0;
		
		/**
		*  This method is called to destory protocol object
		*
		*  @param type		:type is protocol type
		*  @param protocol	:protocol is protocol interface pointer
		*
		**/
		virtual void destoryProtocol(EProtocolType type, IProtocol* protocol) = 0;

		/**
		*  This method is called asynchronously 
		*
		*  @param flag		:read definition of the enum EFlag
		*
		**/
		virtual void setFlag(EFlag flag) = 0;
	};

} // namespace DRIVER end

extern "C"
{
	DRIVER_API DRIVER::IDriver* createDriver();

	DRIVER_API void destoryDriver(DRIVER::IDriver* p);
}

#endif
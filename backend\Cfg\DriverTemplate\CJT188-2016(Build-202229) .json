{"name": "CJT188-2016(Build-202229)", "type": "empty", "category": "户用计量仪表数据传输通讯协议", "categoryNumber": 11, "scriptType": "c++", "information": "户用计量仪表数据传输通讯协议", "2DModel": [], "3DModel": [], "version": 1, "iemsProtocolClass": "cjt188", "iemsProtocolInterfaceType": "rs485", "argument": [{"name": "Address", "label": "设备号", "description": "设备号", "required": true, "inputType": "input", "type": "string", "defaultValue": "", "_level": 2}, {"name": "MeterType", "label": "类型", "description": "类型", "required": true, "inputType": "select", "_level": 2, "items": [{"text": "冷水水表", "value": "16"}, {"text": "生活热水水表", "value": "17"}, {"text": "直饮水水表", "value": "18"}, {"text": "中水水表", "value": "19"}, {"text": "热量表(记热量)", "value": "32"}, {"text": "热量表(记冷量)", "value": "33"}, {"text": "燃气表", "value": "48"}, {"text": "电度表", "value": "64"}], "type": "number", "defaultValue": "16"}, {"name": "__unuse", "label": "__unuse", "description": "__unuse", "required": false, "inputType": "input", "type": "string", "defaultValue": "", "_level": 3}, {"name": "DataType", "label": "数据类型", "description": "数据类型", "required": true, "inputType": "select", "type": "number", "defaultValue": 1, "_level": 4, "items": [{"text": "当前累计流量", "value": 1}]}], "attribute": [{"code": "_online", "name": "在线状态", "type": "DI", "value": {"ON": "在线", "OFF": "离线"}, "history": {"change": false}, "pointType": "internalState", "description": "设备在线状态"}], "classType": "link", "connection": [{"name": "DeviceName", "label": "串口", "description": "串口地址", "required": true, "inputType": "select", "type": "string", "defaultValue": "/dev/ttymxc6", "visible": true, "_level": 1, "useIEMSCom": true, "items": [{"text": "COM1", "value": "/dev/ttymxc6"}, {"text": "COM2", "value": "/dev/ttymxc7"}, {"text": "COM3", "value": "/dev/ttymxc1"}, {"text": "COM4", "value": "/dev/ttymxc3"}]}, {"name": "BaudRate", "label": "波特率", "description": "波特率", "required": true, "type": "number", "visible": true, "min": 110, "max": 921600, "defaultValue": 9600, "inputType": "select", "_level": 1, "items": [{"text": "110", "value": 110}, {"text": "300", "value": 300}, {"text": "600", "value": 600}, {"text": "1200", "value": 1200}, {"text": "2400", "value": 2400}, {"text": "4800", "value": 4800}, {"text": "9600", "value": 9600}, {"text": "14400", "value": 14400}, {"text": "19200", "value": 19200}, {"text": "38400", "value": 38400}, {"text": "57600", "value": 57600}, {"text": "115200", "value": 115200}, {"text": "230400", "value": 230400}, {"text": "380400", "value": 380400}, {"text": "460800", "value": 460800}, {"text": "921600", "value": 921600}]}, {"name": "Parity", "label": "校验位", "description": "奇偶校验位", "required": true, "type": "string", "visible": true, "defaultValue": "N", "inputType": "select", "_level": 1, "items": [{"text": "None", "value": "N"}, {"text": "Odd", "value": "O"}, {"text": "Even", "value": "E"}, {"text": "<PERSON>", "value": "M"}, {"text": "Space", "value": "S"}]}, {"name": "DataBit", "label": "数据位", "description": "数据位", "required": true, "type": "number", "visible": true, "min": 5, "max": 8, "defaultValue": 8, "inputType": "select", "_level": 1, "items": [{"text": "5", "value": 5}, {"text": "6", "value": 6}, {"text": "7", "value": 7}, {"text": "8", "value": 8}]}, {"name": "StopBit", "label": "停止位", "description": "停止位", "required": true, "type": "number", "visible": true, "min": 1, "max": 2, "defaultValue": 1, "inputType": "select", "_level": 1, "items": [{"text": "1", "value": 1}, {"text": "1.5", "value": 1.5}, {"text": "2", "value": 2}]}, {"name": "Version", "label": "版本", "description": "版本", "type": "string", "inputType": "input", "defaultValue": "2016", "_level": 1, "visible": false}], "driverFile": {"Linux ARMv7": "DriverEMSBoxCJT188_build202528"}, "description": "用户计量仪表数据传输协议(CJT188-2016)", "systemClass": 1, "creationTime": 1612493004344, "systemVersion": "0.0.1", "modificationTime": 1612511289523, "argumentAscription": "point"}
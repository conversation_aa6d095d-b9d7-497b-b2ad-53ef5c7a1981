#include "DriverEngine.h"

#include <iostream>
#include <algorithm>

#include "Utils/Utils.hpp"
#include "ADriverFactory.h"

namespace DRIVER
{
    CDriverEngine::CDriverEngine() : thread_(nullptr), cb_(nullptr)
    {
    }

    CDriverEngine::~CDriverEngine()
    {
        deleteAllDriver();
    }

    void CDriverEngine::loop()
    {
    }

    CDriverChannelSptr CDriverEngine::getDriver(const std::string& driverName)
    {
        //std::unique_lock<std::mutex> lock(m_mapDriverMutex);
        CDriverChannelSptr ret;
        auto driverIter = mapDriver_.find(driverName);
        if (driverIter != mapDriver_.end())
        {
            ret = driverIter->second;
        }
        return ret;
    }

    CDriverChannelSptr CDriverEngine::getDriverByDeviceName(const std::string& deviceName)
    {
        CDriverChannelSptr ret = nullptr;
        //std::unique_lock<std::mutex> lock(m_mapDriverMutex);
        for (auto& iter : mapDriver_)
        {
            if (iter.second->hasDevice(deviceName))
            {
                ret = iter.second;
                break;
            }
        }

        return ret;
    }

    void CDriverEngine::setCallback(IEngineCallback* cb)
    {
        cb_ = cb;
    }

    bool CDriverEngine::configure(const SEngineConfig& cfg)
    {
        // 20K
        auto max_size = 1024 * 20;
        auto max_files = 1;
        logger_ = spdlog::rotating_logger_mt(cfg.logFile, cfg.logFile, max_size, max_files);
        return true;
    }

    bool CDriverEngine::run()
    {
        running_ = true;
        //thread_ = new std::thread(std::bind(&CDriverEngine::loop, this));
        return true;
    }

    bool CDriverEngine::stop()
    {
        running_ = false;
        /*if (thread_->joinable())
        {
            thread_->join();
        }
        else
        {
            thread_->detach();
        }

        delete thread_;
        thread_ = nullptr;*/

        return true;
    }

    bool CDriverEngine::addDriver(const std::string& driverName, const SChannelConfig& cfg)
    {
        if (driverName.empty())
        {
            return false;
        }

        deleteDriver(driverName);

        CDriverChannelSptr driver(new CDriverChannel(driverName, cfg.driverType));
        driver->setCallback(this);
        if (!driver->initial(cfg))
        {
            logger_->error("[{0}] initial failed", cfg.driverType.c_str());
            return false;
        }
        
        if (!driver->start())
        {
            logger_->error("[{0}] start failed", cfg.driverType.c_str());
            driver->uninitial();
            return false;
        }
        //std::unique_lock<std::mutex> lock(m_mapDriverMutex);
        mapDriver_.emplace(driverName, driver);
        return true;
    }

    void CDriverEngine::deleteDriver(const std::string& driverName)
    {
        //std::unique_lock<std::mutex> lock(m_mapDriverMutex);
        auto driverIter = mapDriver_.find(driverName);
        if (driverIter != mapDriver_.end())
        {
            driverIter->second->stop();
            driverIter->second->uninitial();
            mapDriver_.erase(driverIter);

            std::cout << "Delete driver : " << driverName << std::endl;
            logger_->info("Delete driver : Driver [{0}]", driverName.c_str());
        }
        else
        {
            logger_->error("Delete driver failed. error : Driver [{0}] not found", driverName.c_str());
        }
    }

    void CDriverEngine::deleteAllDriver()
    {
        if(mapDriver_.empty())
        {
            return;
        }
        for (auto driverIter = mapDriver_.begin(); driverIter != mapDriver_.end(); ++driverIter)
        {
            if(driverIter->second)
            {
                std::cout << __func__ << "    start stop driver : " << driverIter->first << std::endl;
                driverIter->second->stop();
                driverIter->second->uninitial();
                std::cout << __func__ << "    end stop driver : " << driverIter->first << std::endl;
            }
        }
        mapDriver_.clear();
    }

    bool CDriverEngine::hasDriver(const std::string& driverName)
    {
        auto driverIter = mapDriver_.find(driverName);
        return driverIter != mapDriver_.end();
    }

    bool CDriverEngine::getDriverOpenStatus(const std::string& driverName)
    {
        bool ret = false;
        auto driverIter = mapDriver_.find(driverName);
        if (driverIter != mapDriver_.end())
        {
           ret = driverIter->second->getStatus();
        }
        return ret;
    }

    int CDriverEngine::getDriverConnectQuality(const std::string& driverName)
    {
        int ret = 0;
        auto driverIter = mapDriver_.find(driverName);
        if (driverIter != mapDriver_.end())
        {
            ret = driverIter->second->getConnectQuality();
        }
        return ret;
    }

    void CDriverEngine::getChannelNames(std::vector<std::string>& channelNames)
    {
        for (const auto& iter : mapDriver_)
        {
            channelNames.push_back(iter.first);
        }
    }

    bool CDriverEngine::getDeviceOnlineStatus(const std::string& channelName, ChannelDataSet& dataSet)
    {
        bool ret = false;
        auto driver = getDriver(channelName);
        if (driver)
        {
            ret = eStatusSuccess == driver->getDeviceOnlineStatus(dataSet);
        }
        return ret;
    }

    bool CDriverEngine::getChannelOnline(const std::string& channelName, ChannelDataSet& dataSet)
    {
        bool ret = false;
        auto driver = getDriver(channelName);
        if (driver)
        {
            ret = eStatusSuccess == driver->getChannelOnline(dataSet);
        }
        return ret;
    }

    bool CDriverEngine::sendAllData(const std::string& channelName)
    {
        bool ret = false;
        auto driver = getDriver(channelName);
        if (driver)
        {
            ret = eStatusSuccess == driver->sendAllData();
        }
        return ret;
    }

    bool CDriverEngine::getBufferedData(const std::string& channelName, ChannelDataSet& dataSet)
    {
        bool ret = false;
        auto driver = getDriver(channelName);
        if (driver)
        {
            ret = eStatusSuccess == driver->getBufferedData(dataSet);
        }
        return ret;
    }

    bool CDriverEngine::getBufferedData(const std::string& channelName, const std::string& deviceName, DeviceDataSet& dataSet, bool isWeb)
    {
        bool ret = false;
        auto driver = getDriver(channelName);
        if (driver)
        {
            ret = eStatusSuccess == driver->getBufferedData(deviceName, dataSet, isWeb);
        }
        return ret;
    }

    bool CDriverEngine::getBufferedData(const std::string& channelName, const std::string& deviceName, const std::string& pointName, SData& data)
    {
        bool ret = false;
        auto driver = getDriver(channelName);
        if (driver)
        {
            ret = eStatusSuccess == driver->getBufferedData(deviceName, pointName, data);
        }
        return ret;
    }

    bool CDriverEngine::control(const std::vector<SCtrlInfo>& ctrlInfos)
    {
        bool ret = false;
        if (ctrlInfos.size() == 1)
        {
            ret = convertSingleControl(ctrlInfos[0]);
        }
        else
        {
            ret = convertBatchControl(ctrlInfos);
        }
        
        return ret;
    }

    bool CDriverEngine::controlSet(const std::string& channelName, const std::unordered_map<std::string, std::unordered_map<DRIVER::SCtrlInfo::EType, std::vector<DRIVER::SCtrlInfo>>>& ctrlInfos)
    {
        //std::cout << channelName << std::endl;
        CDriverChannelSptr channelSptr = getDriver(channelName);
        if (!channelSptr)
            return false;

        std::unordered_map<std::string, std::unordered_map<SControlInfo::EType, std::vector<SControlInfo>>> ctrls;

        for (auto dit : ctrlInfos)
        {
            for (auto tit : dit.second)
            {
                for (auto pit : tit.second)
                {
                    SControlInfo ctlinfo;
                    ctlinfo.controlType = SControlInfo::EType(tit.first);
                    ctlinfo.sourceID = pit.source;
                    ctlinfo.sequence = pit.sequence;
                    ctlinfo.channelName = channelName;
                    ctlinfo.deviceName = pit.deviceName;
                    ctlinfo.pointName = ctlinfo.pointName;
                    ctlinfo.value = pit.value;
                    ctrls[dit.first][SControlInfo::EType(tit.first)].emplace_back(ctlinfo);
                }
            }
        }

        
        return channelSptr->controlSet(ctrls);
    }

    bool CDriverEngine::convertSingleControl(const SCtrlInfo& ctrlInfo)
    {
        bool ret = false;
        SControlInfo pointControl;
        pointControl.sourceID = ctrlInfo.source;
        pointControl.sequence = ctrlInfo.sequence;
        pointControl.channelName = ctrlInfo.channelName;
        pointControl.deviceName = ctrlInfo.deviceName;
        pointControl.pointName = ctrlInfo.pointName;
        pointControl.value = ctrlInfo.value;
        pointControl.raw = ctrlInfo.raw;

        CDriverChannelSptr channelSptr;
        if (ctrlInfo.channelName.empty())
        {
            channelSptr = getDriverByDeviceName(ctrlInfo.deviceName);
        }
        else
        {
            channelSptr = getDriver(ctrlInfo.channelName);
        }

        if (!channelSptr)
        {
            return ret;
        }

        switch (ctrlInfo.type)
        {
        case SCtrlInfo::eTLSM:
        case SCtrlInfo::eKZYZ:
        case SCtrlInfo::eJDGP:
        case SCtrlInfo::eWXGP:
            ret = channelSptr->offlineDevice(pointControl);
            break;
        case SCtrlInfo::eOfflinePoint:
            ret = channelSptr->offlinePoint(pointControl) == eStatusSuccess;
            break;
        case SCtrlInfo::eOnlinePoint:
            ret = channelSptr->onlinePoint(pointControl) == eStatusSuccess;
            break;
        case SCtrlInfo::emanset:
            ret = channelSptr->setArtificialValue(pointControl) == eStatusSuccess;
            break;
        case SCtrlInfo::eWrite:
            if (ctrlInfo.async)
            {
                pointControl.controlType = SControlInfo::EType::eWrite;
                channelSptr->control(pointControl);
                ret = true;
            }
            else
            {
                pointControl.controlType = SControlInfo::EType::eWrite;
                ret = channelSptr->control(pointControl).get().status == SControlFeedback::eFinished;
            }
            break;
        default:
            break;
        }

        return ret;
    }

    bool CDriverEngine::convertBatchControl(const std::vector<SCtrlInfo>& ctrlInfos)
    {
        bool ret = false;

        CDriverChannelSptr channelSptr;
        if (ctrlInfos[0].channelName.empty())
        {
            channelSptr = getDriverByDeviceName(ctrlInfos[0].deviceName);
        }
        else channelSptr = getDriver(ctrlInfos[0].channelName);

        if (!channelSptr)
        {
            return ret;
        }
        std::vector<SControlInfo> scontrolInfos;
        for (auto ctrlInfo : ctrlInfos)
        {
            SControlInfo pointControl;
            pointControl.sourceID = ctrlInfo.source;
            pointControl.sequence = ctrlInfo.sequence;
            pointControl.channelName = ctrlInfo.channelName;
            pointControl.deviceName = ctrlInfo.deviceName;
            pointControl.pointName = ctrlInfo.pointName;
            pointControl.value = ctrlInfo.value;
            pointControl.raw = ctrlInfo.raw;

            switch (ctrlInfo.type)
            {
            case SCtrlInfo::eTLSM:
            case SCtrlInfo::eKZYZ:
            case SCtrlInfo::eJDGP:
            case SCtrlInfo::eWXGP:
                ret = channelSptr->offlineDevice(pointControl);
                break;
            case SCtrlInfo::eOfflinePoint:
                ret = channelSptr->offlinePoint(pointControl) == eStatusSuccess;
                break;
            case SCtrlInfo::eOnlinePoint:
                ret = channelSptr->onlinePoint(pointControl) == eStatusSuccess;
                break;
            case SCtrlInfo::emanset:
                ret = channelSptr->setArtificialValue(pointControl) == eStatusSuccess;
                break;
            case SCtrlInfo::eWrite:
                if (ctrlInfo.async)
                {
                    pointControl.controlType = SControlInfo::EType::eWrite;
                    scontrolInfos.push_back(pointControl);
                }
                else
                {
                    ret = channelSptr->control(pointControl).get().status == SControlFeedback::eFinished;
                }
                break;
            default:
                break;
            }
        }

        if (!scontrolInfos.empty())
        {
            channelSptr->control(scontrolInfos);
            ret = true;
        }
        
        return true;
    }

    void CDriverEngine::onChannelStatus(const std::string& channelName, const SStatus& status)
    {
        if (cb_) cb_->onEngineStatus(channelName, status);
    }

    void CDriverEngine::onChannelOnline(const std::string& channelName, bool online)
    {
        if (cb_) cb_->onEngineOnline(channelName, online);
    }

    void CDriverEngine::onChannelData(const std::string& channelName, ChannelDataSetSptr dataSetSptr)
    {
        if (cb_) cb_->onEngineData(channelName, dataSetSptr);
    }

    void CDriverEngine::onChannelHistoryData(const std::string& channelName, ChannelHistoryDataSetSptr dataSetSptr)
    {
        if (cb_) cb_->onEngineHistoryData(channelName, dataSetSptr);
    }
    
    void CDriverEngine::onChannelEvent(const std::string& channelName, const std::string& deviceName, SEventSptr eventSptr)
    {
        if (cb_) cb_->onEngineEvent(channelName, deviceName, eventSptr);
    }
    
    void CDriverEngine::onChannelControlFeedback(const std::string& channelName, const SControlFeedback& feedback)
    {
        if (cb_) cb_->onEngineControlFeedback(channelName, feedback);
    }

    void CDriverEngine::onChannelUpdateDeviceOffline(const std::string& channelName, bool isOffline)
    {
        if (cb_) cb_->onEngineUpdateDeviceOffline(channelName, isOffline);
    }

    void CDriverEngine::onChannelUpdateDeviceScanoff(const std::string& channelName, std::string& deviceName, bool scanoff)
    {
        if (cb_) cb_->onEngineUpdateDeviceScanoff(channelName, deviceName, scanoff);
    }

    void CDriverEngine::onChannelUpdateDeviceInhibit(const std::string& channelName, std::string& deviceName, bool inhibit)
    {
        if (cb_) cb_->onEngineUpdateDeviceInhibit(channelName, deviceName, inhibit);
    }
} //namespace DRIVER

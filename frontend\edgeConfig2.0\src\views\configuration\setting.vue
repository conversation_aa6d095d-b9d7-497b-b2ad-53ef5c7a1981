<template>
  <div class="container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <div class="header-content">
            <!-- 组态模板选择器和新建按钮 -->
            <div class="template-selector">
              <el-select 
                v-model="selectedTemplateId" 
                placeholder="选择组态模板" 
                @change="handleTemplateChange"
                style="width: 200px; margin-right: 8px;"
              >
                <el-option 
                  v-for="template in configTemplates" 
                  :key="template.id" 
                  :label="template.name" 
                  :value="template.id"
                >
                  <div class="template-option">
                    <span>{{ template.name }}</span>
                    <div class="template-actions">
                      <el-button 
                        link
                        type="primary" 
                        size="small" 
                        icon="Edit" 
                        @click.stop="handleEditTemplate(template)"
                      ></el-button>
                      <el-button 
                        link
                        type="danger" 
                        size="small" 
                        icon="Delete" 
                        @click.stop="handleDeleteTemplate(template)"
                      ></el-button>
                    </div>
                  </div>
                </el-option>
              </el-select>
              <el-button type="primary" size="small" @click="handleCreateTemplate">新建</el-button>
            </div>
            
            <!-- 按钮靠右 -->
            <div class="header-buttons">
              <!-- 隐藏从组态同步按钮 -->
              <!-- <el-button type="primary" @click="syncFromConfig">从组态同步</el-button> -->
              <!-- 添加组态生效按钮 -->
              <el-button type="success" @click="applyConfiguration" :disabled="isConfigModified || checkUnsavedChanges()">组态生效</el-button>
              <el-button 
                type="primary" 
                @click="saveConfiguration"
                :disabled="saveButtonDisabled"
                :class="saveButtonClass"
              >
                保存配置
                <el-badge 
                  v-if="isConfigModified || checkUnsavedChanges()" 
                  :value="getModifiedCount()" 
                  class="changes-badge"
                />
              </el-button>
              <el-button type="primary" @click="exportConfig">导出配置</el-button>
              <el-button type="primary" @click="importConfig">导入配置</el-button>
            </div>
          </div>
        </div>
      </template>
      <div class="card-content">
        <div class="content-layout">
          <div class="left-panel" @contextmenu.prevent="handleContextMenu">
            <!-- 添加标题和按钮 -->
            <div class="list-header">
              <span>组态列表</span>
              <el-button type="primary" size="small" @click="handleAddPrototype">添加</el-button>
            </div>
            <!-- 左侧协议原型树 -->
            <el-tree
              ref="protocolTreeRef"
              :data="protocolList"
              node-key="id"
              :props="defaultProps"
              @node-click="handleNodeClick"
              @node-contextmenu="handleNodeContextMenu"
              highlight-current
              default-expand-all
              :expand-on-click-node="false"
              class="protocol-tree"
            >
              <template #default="{ node, data }">
                <span class="custom-tree-node" :class="{ 'node-selected': currentProtocol && currentProtocol.id === data.id }">
                  <span>{{ node.label }}</span>
                </span>
              </template>
            </el-tree>
            
            <!-- 右键菜单 - 空白区域 -->
            <div 
              v-show="contextMenu.visible" 
              class="context-menu"
              :style="{left: contextMenu.x + 'px', top: contextMenu.y + 'px'}"
            >
              <div class="context-menu-item" @click="handleAddPrototype">添加协议原型</div>
            </div>
            
            <!-- 右键菜单 - 原型节点 -->
            <div 
              v-show="nodeContextMenu.visible" 
              class="context-menu"
              :style="{left: nodeContextMenu.x + 'px', top: nodeContextMenu.y + 'px'}"
            >
              <div class="context-menu-item" @click="handleAddInstance">添加协议实例</div>
              <div class="context-menu-item" @click="handleDeletePrototype">删除协议原型</div>
            </div>
            
            <!-- 右键菜单 - 实例节点 -->
            <div 
              v-show="instanceContextMenu.visible" 
              class="context-menu"
              :style="{left: instanceContextMenu.x + 'px', top: instanceContextMenu.y + 'px'}"
            >
              <div class="context-menu-item" @click="handleAddDevice">添加设备</div>
              <div class="context-menu-item" @click="handleAddDeviceFromTemplate">从设备模板添加</div>
              <div class="context-menu-item" @click="handleEditInstance">编辑协议实例</div>
              <div class="context-menu-item" @click="handleDeleteInstance">删除协议实例</div>
            </div>
            
            <!-- 右键菜单 - 设备节点 -->
            <div 
              v-show="deviceContextMenu.visible" 
              class="context-menu"
              :style="{left: deviceContextMenu.x + 'px', top: deviceContextMenu.y + 'px'}"
            >
              <div class="context-menu-item" @click="handleEditDevice">编辑设备</div>
              <div class="context-menu-item" @click="handleDeleteDevice">删除设备</div>
            </div>
          </div>
          <div class="right-panel">
            <!-- 右侧点表 -->
            <div class="table-header">
              <span class="table-title">位号列表</span>
              <span class="table-tip">提示：双击单元格可以直接编辑</span>
            </div>
            <el-table
              :data="displayPointsList"
              style="width: 100%; height: calc(100% - 30px)"
              border
              stripe
              @row-contextmenu="handlePointContextMenu"
              @contextmenu.prevent="handleTableContextMenu"
            >
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="code" label="位号名称" min-width="20%" align="center">
                <template #default="scope">
                  <div class="editable-cell" @dblclick="handleCellDbClick(scope.row, 'code')">
                    {{ scope.row.code }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="位号描述" min-width="30%" align="center">
                  <template #default="scope">
                    <div class="editable-cell" @dblclick="handleCellDbClick(scope.row, 'name')">
                      {{ scope.row.name }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="address" label="寄存器地址" min-width="20%" align="center">
                <template #default="scope">
                  <div 
                    class="editable-cell" 
                    :class="{ 'disabled-field': pointAddressConfig && pointAddressConfig.required === false }"
                    @dblclick="handleCellDbClick(scope.row, 'address')"
                  >
                    <span v-if="pointAddressConfig && pointAddressConfig.required === false" class="disabled-text">
                      不需要填写
                    </span>
                    <span v-else>
                      {{ getDisplayValue(scope.row.address, 'address') }}
                    </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="数据类型" min-width="15%" align="center">
                <template #default="scope">
                  <div 
                    class="editable-cell" 
                    :class="{ 'disabled-field': pointTypeConfig && pointTypeConfig.required === false }"
                    @dblclick="handleCellDbClick(scope.row, 'type')"
                  >
                    <span v-if="pointTypeConfig && pointTypeConfig.required === false" class="disabled-text">
                      不需要填写
                    </span>
                    <span v-else>
                      {{ getDisplayValue(scope.row.type, 'type') }}
                    </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center">
                <template #default="scope">
                  <!-- <el-button type="primary" link size="small" @click="handleEditPoint(scope.row)">编辑</el-button> -->
                  <el-button type="danger" link size="small" @click="handleDeletePoint(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!-- 点位右键菜单 -->
            <div 
              v-show="pointContextMenu.visible" 
              class="context-menu"
              :style="{left: pointContextMenu.x + 'px', top: pointContextMenu.y + 'px'}"
            >
              <div class="context-menu-item" @click="handleAddPoint">新增位号</div>
              <div class="context-menu-item" @click="handleBatchAddPoints">批量新增位号</div>
              <div class="context-menu-item" @click="handleEditPoint(pointContextMenu.data)" v-if="pointContextMenu.data">修改位号</div>
              <div class="context-menu-item" @click="handleDeletePoint(pointContextMenu.data)" v-if="pointContextMenu.data">删除位号</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    
    <!-- 添加协议原型对话框 -->
    <el-dialog
      v-model="prototypeDialog.visible"
      title="添加协议原型"
      width="30%"
    >
      <el-form :model="prototypeForm" label-width="100px">
        <el-form-item label="协议类型">
          <el-select v-model="prototypeForm.type" placeholder="请选择协议类型">
            <el-option v-for="item in protocolTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="prototypeDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddPrototype">确认</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 添加协议实例对话框 -->
    <el-dialog
      v-model="instanceDialog.visible"
      title="添加协议实例"
      width="30%"
    >
      <el-form :model="instanceForm" label-width="120px">
        <el-form-item label="原型">
          <el-input v-model="instanceForm.prototypeName" disabled />
        </el-form-item>
        <el-form-item label="实例名称">
          <el-input v-model="instanceForm.name" placeholder="请输入实例名称" />
        </el-form-item>
        <!-- 动态渲染channel参数 -->
        <el-form-item 
          v-for="param in instanceForm.channelParams" 
          :key="param.name" 
          :label="param.label"
        >
          <!-- 如果是select类型，使用下拉选择框 -->
          <el-select 
            v-if="param.inputType === 'select'"
            v-model="param.value" 
            :placeholder="`请选择${param.label}`"
            :disabled="param.visible === false"
            style="width: 100%"
          >
            <el-option
              v-for="item in param.items"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            />
          </el-select>
          <!-- 否则使用输入框 -->
          <el-input 
            v-else
            v-model="param.value" 
            :placeholder="`请输入${param.label}`"
            :disabled="param.visible === false"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="instanceDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddInstance">确认</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 添加设备对话框 -->
    <el-dialog
      v-model="deviceDialog.visible"
      title="添加设备"
      width="30%"
    >
      <el-form :model="deviceForm" label-width="120px">
        <el-form-item label="实例">
          <el-input v-model="deviceForm.instanceName" disabled />
        </el-form-item>
        <el-form-item label="设备名称">
          <el-input v-model="deviceForm.name" placeholder="请输入设备名称" />
        </el-form-item>
        <!-- 动态渲染device参数 -->
        <el-form-item 
          v-for="param in deviceForm.deviceParams" 
          :key="param.name" 
          :label="param.label"
        >
          <!-- 如果是select类型，使用下拉选择框 -->
          <el-select 
            v-if="param.inputType === 'select'"
            v-model="param.value" 
            :placeholder="`请选择${param.label}`"
            :disabled="param.visible === false"
            style="width: 100%"
          >
            <el-option
              v-for="item in param.items"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            />
          </el-select>
          <!-- 否则使用输入框 -->
          <el-input 
            v-else
            v-model="param.value" 
            :placeholder="`请输入${param.label}`"
            :disabled="param.visible === false"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deviceDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddDevice">确认</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 从设备模板添加设备对话框 -->
    <el-dialog
      v-model="templateDeviceDialog.visible"
      title="从设备模板添加设备"
      width="30%"
    >
      <el-form :model="templateDeviceForm" label-width="100px">
        <el-form-item label="协议实例">
          <el-input v-model="templateDeviceForm.instanceName" disabled />
        </el-form-item>
        <el-form-item label="设备名称" required>
          <el-input v-model="templateDeviceForm.name" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备模板" required>
          <el-select v-model="templateDeviceForm.templateId" placeholder="请选择设备模板" style="width: 100%">
            <el-option
              v-for="item in deviceTemplates"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <!-- 动态渲染device参数 -->
        <el-form-item 
          v-for="param in templateDeviceForm.deviceParams" 
          :key="param.name" 
          :label="param.label"
        >
          <!-- 如果是select类型，使用下拉选择框 -->
          <el-select 
            v-if="param.inputType === 'select'"
            v-model="param.value" 
            :placeholder="`请选择${param.label}`"
            :disabled="param.visible === false"
            style="width: 100%"
          >
            <el-option
              v-for="item in param.items"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            />
          </el-select>
          <!-- 否则使用输入框 -->
          <el-input 
            v-else
            v-model="param.value" 
            :placeholder="`请输入${param.label}`"
            :disabled="param.visible === false"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="templateDeviceDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddTemplateDevice">确认</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 添加/编辑点位对话框 -->
    <el-dialog
      v-model="pointDialog.visible"
      :title="pointDialog.title"
      width="30%"
    >
      <el-form :model="pointForm" :rules="pointRules" ref="pointFormRef" label-width="100px">
        <el-form-item label="位号名称" prop="code">
          <el-input v-model="pointForm.code" placeholder="请输入位号名称" />
        </el-form-item>
        <el-form-item label="位号描述" prop="name">
          <el-input v-model="pointForm.name" placeholder="请输入位号描述" />
        </el-form-item>
        <el-form-item 
          v-if="pointAddressConfig && pointAddressConfig.label!== '__unuse'"
          :label="pointAddressConfig ? pointAddressConfig.label : '寄存器地址'" 
          prop="address"
          :required="pointAddressConfig && pointAddressConfig.required === true"
        >
          <!-- 如果地址字段是select类型，使用下拉选择框 -->
          <el-select 
            v-if="pointAddressConfig && pointAddressConfig.inputType === 'select'"
            v-model="pointForm.address" 
            :placeholder="pointAddressConfig.required === false ? '不需要填写' : `请选择${pointAddressConfig.label || '寄存器地址'}`"
            :disabled="pointAddressConfig && pointAddressConfig.required === false"
            style="width: 100%"
          >
            <el-option
              v-for="item in pointAddressConfig.items"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            />
          </el-select>
          <!-- 否则使用输入框 -->
          <el-input 
            v-else
            v-model="pointForm.address" 
            :placeholder="pointAddressConfig && pointAddressConfig.required === false ? '不需要填写' : `请输入${pointAddressConfig ? pointAddressConfig.label : '寄存器地址'}`"
            :disabled="pointAddressConfig && pointAddressConfig.required === false"
          />
        </el-form-item>
        <el-form-item 
          :label="pointTypeConfig ? pointTypeConfig.label : '数据类型'" 
          prop="type"
          :required="pointTypeConfig && pointTypeConfig.required === true"
        >
          <!-- 如果数据类型字段是select类型，使用下拉选择框 -->
          <el-select 
            v-if="pointTypeConfig && pointTypeConfig.inputType === 'select'"
            v-model="pointForm.type" 
            :placeholder="pointTypeConfig.required === false ? '不需要填写' : `请选择${pointTypeConfig.label || '数据类型'}`"
            :disabled="pointTypeConfig && pointTypeConfig.required === false"
            style="width: 100%"
          >
            <el-option
              v-for="item in pointTypeConfig.items"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            />
          </el-select>
          <!-- 否则使用输入框 -->
          <el-input 
            v-else
            v-model="pointForm.type" 
            :placeholder="pointTypeConfig && pointTypeConfig.required === false ? '不需要填写' : `请输入${pointTypeConfig ? pointTypeConfig.label : '数据类型'}`"
            :disabled="pointTypeConfig && pointTypeConfig.required === false"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="pointDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddOrEditPoint">确认</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 编辑协议实例对话框 -->
    <el-dialog
      v-model="editInstanceDialog.visible"
      title="编辑协议实例"
      width="30%"
    >
      <el-form :model="editInstanceForm" label-width="120px">
        <el-form-item label="原型">
          <el-input v-model="editInstanceForm.prototypeName" disabled />
        </el-form-item>
        <el-form-item label="实例名称">
          <el-input v-model="editInstanceForm.name" placeholder="请输入实例名称" />
        </el-form-item>
        <!-- 动态渲染channel参数 -->
        <el-form-item 
          v-for="param in editInstanceForm.channelParams" 
          :key="param.name" 
          :label="param.label"
        >
          <!-- 如果是select类型，使用下拉选择框 -->
          <el-select 
            v-if="param.inputType === 'select'"
            v-model="param.value" 
            :placeholder="`请选择${param.label}`"
            :disabled="param.visible === false"
            style="width: 100%"
          >
            <el-option
              v-for="item in param.items"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            />
          </el-select>
          <!-- 否则使用输入框 -->
          <el-input 
            v-else
            v-model="param.value" 
            :placeholder="`请输入${param.label}`"
            :disabled="param.visible === false"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editInstanceDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmEditInstance">确认</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 编辑设备对话框 -->
    <el-dialog
      v-model="editDeviceDialog.visible"
      title="编辑设备"
      width="30%"
    >
      <el-form :model="editDeviceForm" label-width="100px">
        <el-form-item label="实例">
          <el-input v-model="editDeviceForm.instanceName" disabled />
        </el-form-item>
        <el-form-item label="设备名称">
          <el-input v-model="editDeviceForm.name" placeholder="请输入设备名称" />
        </el-form-item>
        <!-- 动态渲染device参数 -->
        <el-form-item 
          v-for="param in editDeviceForm.deviceParams" 
          :key="param.name" 
          :label="param.label"
        >
          <!-- 如果是select类型，使用下拉选择框 -->
          <el-select 
            v-if="param.inputType === 'select'"
            v-model="param.value" 
            :placeholder="`请选择${param.label}`"
            :disabled="param.visible === false"
            style="width: 100%"
          >
            <el-option
              v-for="item in param.items"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            />
          </el-select>
          <!-- 否则使用输入框 -->
          <el-input 
            v-else
            v-model="param.value" 
            :placeholder="`请输入${param.label}`"
            :disabled="param.visible === false"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDeviceDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmEditDevice">确认</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 新建组态模板对话框 -->
    <el-dialog
      v-model="createTemplateDialog.visible"
      title="新建组态模板"
      width="30%"
    >
      <el-form :model="createTemplateForm" label-width="100px">
        <el-form-item label="模板名称" required>
          <el-input v-model="createTemplateForm.name" placeholder="请输入模板名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createTemplateDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmCreateTemplate">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑组态模板名称对话框 -->
    <el-dialog
      v-model="editTemplateDialog.visible"
      title="编辑组态模板"
      width="30%"
    >
      <el-form :model="editTemplateForm" label-width="100px">
        <el-form-item label="模板名称" required>
          <el-input v-model="editTemplateForm.name" placeholder="请输入模板名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editTemplateDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmEditTemplate">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量添加点位对话框 -->
    <el-dialog
      v-model="batchPointDialog.visible"
      title="批量新增位号"
      width="40%"
    >
      <el-form :model="batchPointForm" :rules="batchPointRules" ref="batchPointFormRef" label-width="120px">
        <el-form-item label="开始地址" prop="startAddress" v-if="hasBaseAddress">
          <el-input v-model="batchPointForm.startAddress" placeholder="请输入开始地址" />
        </el-form-item>
        <el-form-item label="位号个数" prop="registerCount">
          <el-input-number 
            v-model="batchPointForm.registerCount" 
            :min="1" 
            placeholder="请输入位号个数"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="数据类型" prop="registerType">
          <!-- 如果数据类型字段是select类型，使用下拉选择框 -->
          <el-select 
            v-if="pointTypeConfig && pointTypeConfig.inputType === 'select'"
            v-model="batchPointForm.registerType" 
            placeholder="请选择数据类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in pointTypeConfig.items"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            />
          </el-select>
          <!-- 否则使用输入框 -->
          <el-input 
            v-else
            v-model="batchPointForm.registerType" 
            placeholder="请输入数据类型" 
          />
        </el-form-item>
        <el-form-item label="位号名前缀" prop="namePrefix">
          <el-input v-model="batchPointForm.namePrefix" placeholder="请输入位号名前缀" />
        </el-form-item>
                 <div class="dialog-tip">
           <p>说明：</p>
           <ul>
             <li>位号个数用于控制生成的位号数量</li>
             <li>位号名称格式：前缀 + 序号（如：AI001, AI002...）</li>
             <li>位号描述格式：前缀 + 序号（如：AI001, AI002...）</li>
             <li>地址将从开始地址开始按序号递增生成</li>
           </ul>
         </div>
      </el-form>
      
      <!-- 预览表格 -->
      <div v-if="previewPoints.length > 0" class="preview-section">
        <div class="preview-header">
          <span class="preview-title">预览将要创建的位号（共 {{ previewPoints.length }} 个）</span>
        </div>
        <el-table
          :data="previewPoints"
          style="width: 100%;"
          :height="240"
          border
          stripe
          size="small"
        >
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="code" label="位号名称" min-width="40%" align="center" />
          <el-table-column prop="address" label="寄存器地址" min-width="40%" align="center"  v-if="hasBaseAddress"/>
        </el-table>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelBatchAdd">取消</el-button>
          <el-button type="primary" @click="previewBatchPoints" v-if="previewPoints.length === 0">预览</el-button>
          <el-button type="primary" @click="previewBatchPoints" v-if="previewPoints.length > 0">重新预览</el-button>
          <el-button type="success" @click="confirmBatchAddPoints" v-if="previewPoints.length > 0">确认添加</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="ConfigSetting">
import { ref, reactive, nextTick, onMounted, onBeforeUnmount, computed } from 'vue';
// @ts-ignore
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { GlobalStore } from "@/stores";
// 使用any类型来避免导入错误
// import type { FormInstance } from 'element-plus';
// import type { ElTree } from 'element-plus';
import { 
  getDeviceTemplateList, 
  saveConfigurationTemplate,
  getConfigurationTemplate,
  deleteConfigurationTemplate,
  getConfigLinkTemplate,
  applyLocalConfiguration,
  getConfigRunningState,
  getIEMSConfig
} from "@/api/modules/configuration";
import { onBeforeRouteLeave } from 'vue-router';
import { generateUUID } from "@/utils/util";
import { count } from 'console';

const globalStore = GlobalStore();
const currentEnv = computed(() => globalStore.basicInfo.env);

//批量添加点位是否显示开始地址
const hasBaseAddress = ref<any>(false)

// IEMS配置信息
const IEMSConfigInfo = ref<any>(); 

// 协议树引用
const protocolTreeRef = ref<any>();

// 协议列表数据
const protocolList = ref<any[]>([]);

// 当前选中的协议
const currentProtocol = ref<any>(null);

// 树形配置
const defaultProps = {
  children: 'children',
  label: 'name'
};

// 右键菜单状态 - 空白区域
const contextMenu = reactive({
  visible: false,
  x: 0,
  y: 0
});

// 右键菜单状态 - 原型节点
const nodeContextMenu = reactive({
  visible: false,
  x: 0,
  y: 0,
  node: null as any
});

// 右键菜单状态 - 实例节点
const instanceContextMenu = reactive({
  visible: false,
  x: 0,
  y: 0,
  node: null as any
});

// 右键菜单状态 - 设备节点
const deviceContextMenu = reactive({
  visible: false,
  x: 0,
  y: 0,
  node: null as any
});
// 协议原型对话框
const prototypeDialog = reactive({
  visible: false
});

// 协议实例对话框
const instanceDialog = reactive({
  visible: false
});

// 设备对话框
const deviceDialog = reactive({
  visible: false
});

// 从设备模板添加对话框
const templateDeviceDialog = reactive({
  visible: false
});

// 编辑协议实例对话框
const editInstanceDialog = reactive({
  visible: false
});

// 编辑设备对话框
const editDeviceDialog = reactive({
  visible: false
});

// 协议原型表单
const prototypeForm = reactive({
  type: ''
});

// 协议实例表单
const instanceForm = reactive({
  name: '',
  parentId: '',
  prototypeName: '',
  protocol: [], // 修改为数组，用于存储channel参数
  channelParams: [] as any[] // 用于存储channel参数的描述信息
});

// 设备表单
const deviceForm = reactive({
  name: '',
  parentId: '',
  instanceName: '',
  protocol: [], // 修改为数组，用于存储device参数
  deviceParams: [] as any[] // 用于存储device参数的描述信息
});

// 从设备模板添加表单
const templateDeviceForm = reactive<any>({
  name: '',
  parentId: '',
  instanceName: '',
  templateId: ''
});

// 编辑协议实例表单
const editInstanceForm = reactive({
  id: '',
  name: '',
  prototypeName: '',
  parentId: '',
  protocol: [], // 添加protocol字段存储协议参数
  channelParams: [] as any[] // 添加channelParams字段存储参数描述信息
});

// 编辑设备表单
const editDeviceForm = reactive<any>({
  id: '',
  name: '',
  instanceName: '',
  parentId: ''
});

// 协议类型选项
const protocolTypes = [
  { label: 'ModBusTCP', value: 'ModBusTCP' },
  { label: 'ModbusRTU', value: 'ModbusRTU' },
  { label: 'DL/T645', value: 'DLT645' },
  { label: 'CJ/T188', value: 'CJT188' }
];

// 获取IEMS配置信息
const fetchIEMSConfigInfo = async()=>{
  try {
    const res:any = await getIEMSConfig();
    if(res.result && res.result.resultCode === "0"){
      IEMSConfigInfo.value = res.data
    }
    else {
      ElMessage.error(`${res.result?.resultError || '未知错误'}`);
    }
  }
  catch{
    ElMessage.error('获取IEMS配置信息失败');
  }
}

// 设备模板列表
const deviceTemplates = ref<any[]>([]);

// 获取设备模板列表
const fetchDeviceTemplates = async () => {
  try {
    const res = await getDeviceTemplateList();
    if (res.result && res.result.resultCode === "0") {
      // 检查返回的数据结构
      if (res.data && res.data.template && Array.isArray(res.data.template)) {
        // 解析返回的模板数据
        deviceTemplates.value = res.data.template.map((template: any) => {
          return {
            id: template.id,
            name: template.name,
            description: template.description || '',
            points: template.points || []
          };
        });
        console.log('解析后的模板列表:', deviceTemplates.value);
      } else {
        deviceTemplates.value = [];
        console.warn('返回的数据结构不符合预期:', res.data);
      }
    } else {
      ElMessage.error(`获取模板列表失败: ${res.result?.resultError || '未知错误'}`);
    }
  } catch (error) {
    console.error('获取模板列表失败:', error);
    ElMessage.error('获取设备模板列表失败');
  }
};

// 存储完整的协议模板数据
const protocolTemplates = ref<any[]>([]);

// 添加获取组态配置链路模板的方法
const fetchConfigLinkTemplate = async () => {
  try {
    const res = await getConfigLinkTemplate();
    console.log('组态配置链路模板数据:', res);
    
    if (res.result && res.result.resultCode === "0") {
      // 检查返回的数据结构
      if (res.data && Array.isArray(res.data)) {
        // 保存完整的模板数据，包含level字段
        protocolTemplates.value = res.data;
        
        // 更新协议类型选项
        const newProtocolTypes = res.data.map((template: any) => {
          return { label: template.name, value: template.name };
        });
        
        // 替换原有的协议类型选项
        protocolTypes.length = 0;
        newProtocolTypes.forEach(type => protocolTypes.push(type));
        
        console.log('解析后的协议模板:', protocolTemplates.value);
        console.log('更新后的协议类型选项:', protocolTypes);
      } else {
        console.error('获取组态配置链路模板失败: 数据格式不正确');
      }
    } else {
      console.error('获取组态配置链路模板失败:', res.result?.resultError || '未知错误');
    }
  } catch (error) {
    console.error('获取组态配置链路模板失败:', error);
  }
};

// 点位列表
const pointsList = ref<any[]>([]);

// 点位右键菜单
const pointContextMenu = reactive({
  visible: false,
  x: 0,
  y: 0,
  data: null as any
});

// 点位表单
const pointForm = reactive({
  id: '',
  code: '',
  name: '',
  address: '',
  type: ''
});

// 点位表单验证规则 - 改为计算属性以支持动态规则
const pointRules = computed(() => {
  const rules: any = {
    code: [
      { required: true, message: '请输入位号名称', trigger: 'blur' },
      { pattern: /^[A-Za-z0-9_]+$/, message: '只能包含字母、数字和下划线', trigger: 'blur' }
    ],
    name: [
      { required: true, message: '请输入位号描述', trigger: 'blur' }
    ],
    address: [],
    type: []
  };
  
  // 根据字段配置动态设置验证规则
  if (pointAddressConfig.value && pointAddressConfig.value.required === true) {
    rules.address = [
      { required: true, message: `请${pointAddressConfig.value.inputType === 'select' ? '选择' : '输入'}${pointAddressConfig.value.label || '寄存器地址'}`, trigger: 'blur' }
    ];
  }
  
  if (pointTypeConfig.value && pointTypeConfig.value.required === true) {
    rules.type = [
      { required: true, message: `请${pointTypeConfig.value.inputType === 'select' ? '选择' : '输入'}${pointTypeConfig.value.label || '数据类型'}`, trigger: 'blur' }
    ];
  }
  
  return rules;
});

// 点位对话框
const pointDialog = reactive({
  visible: false,
  title: '新增位号'
});

// 点位表单引用
const pointFormRef = ref<any>();

// 批量新增位号对话框
const batchPointDialog = reactive({
  visible: false
});

// 批量新增位号表单
const batchPointForm = reactive({
  startAddress: '',
  registerCount: 1,
  registerType: '',
  namePrefix: ''
});

// 批量新增位号表单验证规则
const batchPointRules = reactive({
  startAddress: [
    { required: true, message: '请输入开始地址', trigger: 'blur' }
  ],
  registerCount: [
    { required: true, message: '请输入位号个数', trigger: 'blur' },
    { type: 'number', min: 1, message: '位号个数必须大于0', trigger: 'blur' }
  ],
  registerType: [
    { required: true, message: '请选择或输入数据类型', trigger: 'blur' }
  ],
  namePrefix: [
    { required: true, message: '请输入位号名前缀', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9_]+$/, message: '位号名前缀只能包含字母、数字和下划线', trigger: 'blur' }
  ]
});

// 批量新增位号表单引用
const batchPointFormRef = ref<any>();

// 预览位号列表
const previewPoints = ref<any[]>([]);

// 获取当前协议的点位地址配置
const pointAddressConfig = computed(() => {
  if (!currentProtocol.value || currentProtocol.value.type !== 'device') {
    return null;
  }
  
  // 查找当前设备所属的协议原型
  let prototypeNode: any = null;
  for (const prototype of protocolList.value) {
    if (prototype.children) {
      for (const instance of prototype.children) {
        if (instance.children) {
          const device = instance.children.find((d: any) => d.id === currentProtocol.value.id);
          if (device) {
            prototypeNode = prototype;
            break;
          }
        }
      }
      if (prototypeNode) break;
    }
  }
  
  if (!prototypeNode) {
    return null;
  }
  
  // 查找对应的协议模板
  const template = protocolTemplates.value.find(t => t.name === prototypeNode.name);
  if (!template || !template.point) {
    return null;
  }
  
  // 查找地址字段的配置（level为3的字段）
  const addressConfig = template.point.find((p: any) => p.level === 3);
  return addressConfig || null;
});

// 获取当前协议的点位数据类型配置
const pointTypeConfig = computed(() => {
  if (!currentProtocol.value || currentProtocol.value.type !== 'device') {
    return null;
  }
  
  // 查找当前设备所属的协议原型
  let prototypeNode: any = null;
  for (const prototype of protocolList.value) {
    if (prototype.children) {
      for (const instance of prototype.children) {
        if (instance.children) {
          const device = instance.children.find((d: any) => d.id === currentProtocol.value.id);
          if (device) {
            prototypeNode = prototype;
            break;
          }
        }
      }
      if (prototypeNode) break;
    }
  }
  
  if (!prototypeNode) {
    return null;
  }
  
  // 查找对应的协议模板
  const template = protocolTemplates.value.find(t => t.name === prototypeNode.name);
  if (!template || !template.point) {
    return null;
  }
  
  // 查找数据类型字段的配置（level为4的字段）
  const typeConfig = template.point.find((p: any) => p.level === 4);
  return typeConfig || null;
});

// 获取字段的显示值（如果是select类型，显示对应的文本）
const getDisplayValue = (value: any, field: string) => {
  if (!value) return value;
  
  let fieldConfig: any = null;
  if (field === 'address') {
    fieldConfig = pointAddressConfig.value;
  } else if (field === 'type') {
    fieldConfig = pointTypeConfig.value;
  }
  
  // 如果字段配置为select类型，查找对应的文本
  if (fieldConfig && fieldConfig.inputType === 'select' && fieldConfig.items) {
    const item = fieldConfig.items.find((item: any) => String(item.value) === String(value));
    return item ? item.text : value;
  }
  
  return value;
};

// 添加修改状态跟踪
const isConfigModified = ref(false);
const originalConfig = ref<any>(null);

// 保存原始配置，用于检测修改
const saveOriginalConfig = () => {
  originalConfig.value = JSON.parse(JSON.stringify(protocolList.value));
};

// 检查是否有未保存的修改
const checkUnsavedChanges = () => {
  if (!originalConfig.value) return false;
  
  // 深度比较配置内容是否有变化
  return JSON.stringify(originalConfig.value) !== JSON.stringify(protocolList.value);
};

// 获取修改计数
const getModifiedCount = () => {
  // 这里可以根据实际需求返回修改的数量
  // 例如可以计算添加/修改/删除的节点数量
  // 简单起见，这里只返回一个固定值表示有修改
  return isConfigModified.value || checkUnsavedChanges() ? '!' : '';
};

// 计算保存按钮是否应该禁用
const saveButtonDisabled = computed(() => {
  // 如果没有组态模板或没有选中的模板，禁用保存按钮
  return !configTemplates.value || configTemplates.value.length === 0 || !selectedTemplateId.value;
});

// 保存按钮状态
const saveButtonClass = computed(() => {
  return {
    'has-changes': (isConfigModified.value || checkUnsavedChanges()) && !saveButtonDisabled.value
  };
});

// 生成8位随机ID
const generateRandomId = () => {
  // 生成8位随机字母数字组合
  return Math.random().toString(36).substring(2, 10);
};

// 在组件的data部分添加一个新的变量来存储组态ID
const configurationId = ref(generateRandomId());

// 组态模板列表
const configTemplates = ref<any[]>([]);
// 当前选中的组态模板ID
const selectedTemplateId = ref('');

// 添加一个变量来存储原始模板列表
const originalTemplates = ref<any[]>([]);

// 获取组态配置模板
const fetchConfigurationTemplate = async () => {
  try {
    const loading = ElLoading.service({
      lock: true,
      text: '加载组态配置...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    const res = await getConfigurationTemplate();
    console.log('组态配置模板数据:', res);
    
    if (res.result && res.result.resultCode === "0") {
      // 检查返回的数据结构
      if (res.data && res.data.template && Array.isArray(res.data.template)) {
        // 保存所有组态模板
        configTemplates.value = res.data.template;
        
        // 保存原始模板列表，用于还原
        originalTemplates.value = JSON.parse(JSON.stringify(res.data.template));
        
        if (configTemplates.value.length > 0) {
          // 默认选择第一个模板
          selectedTemplateId.value = configTemplates.value[0].id;
          
          // 解析第一个组态模板
          const configTemplate = configTemplates.value[0];
          console.log('解析后的组态模板:', configTemplate);
          
          // 设置组态ID
          configurationId.value = configTemplate.id || generateUUID();
          
          // 解析组态数据，构建协议树
          parseConfigurationTemplate(configTemplate);
          
          // ElMessage.success('组态配置加载成功');
        } else {
          // 如果没有模板，显示空状态
          protocolList.value = [];
          ElMessage.info('没有找到组态模板，请创建新模板');
        }
      } else {
        // 如果没有模板数据，初始化为空列表
        protocolList.value = [];
        configTemplates.value = [];
        originalTemplates.value = [];
        console.warn('返回的数据结构不符合预期或没有组态模板:', res.data);
      }
    } else {
      ElMessage.error(`获取组态配置失败: ${res.result?.resultError || '未知错误'}`);
    }
    
    loading.close();
  } catch (error) {
    console.error('获取组态配置失败:', error);
    ElMessage.error('获取组态配置失败');
  }
};

// 解析组态模板数据，构建协议树
const parseConfigurationTemplate = (configTemplate: any) => {
  console.log('开始解析组态模板:', configTemplate);
  
  // 初始化协议列表
  const newProtocolList: any[] = [];
  
  // 检查是否有link字段
  if (!configTemplate.link || typeof configTemplate.link !== 'object') {
    console.warn('组态模板缺少link字段或格式不正确:', configTemplate);
    protocolList.value = newProtocolList;
    return;
  }
  
  try {
    // 遍历link对象中的每个协议类型
    Object.entries(configTemplate.link).forEach(([protocolType, protocolConfig]: [string, any]) => {
      console.log(`处理协议类型: ${protocolType}`);
      
      // 查找对应的协议模板
      const protoTemplate = protocolTemplates.value.find(t => t.name === protocolType);
      
      // 创建协议原型节点，确保保留 driverFile
      const prototypeNode: any = {
        id: generateUUID(),
        name: protocolType,
        type: 'prototype',
        protocolType: protocolType,
        driverFile: protocolConfig.driverFile || (protoTemplate ? protoTemplate.driverFile : {}), // 优先使用配置中的 driverFile
        children: []
      };
      
      // 获取该协议类型的配置
      if (protocolConfig && protocolConfig.instances && Array.isArray(protocolConfig.instances)) {
        protocolConfig.instances.forEach((instance: any) => {
          console.log(`处理实例: ${instance.name}`);
          
          // 创建实例节点
          const instanceNode: any = {
            id: instance.id || generateUUID(),
            name: instance.name,
            type: 'instance',
            parentId: prototypeNode.id,
            protocol: instance.protocol || {},
            children: []
          };
          
          // 处理实例下的所有设备
          if (instance.devices && Array.isArray(instance.devices)) {
            instance.devices.forEach((device: any) => {
              console.log(`处理设备: ${device.name}`);
              
              // 创建设备节点
              const deviceNode: any = {
                id: device.id || generateUUID(),
                name: device.name,
                type: 'device',
                parentId: instanceNode.id,
                protocol: device.protocol || {},
                points: []
              };
              
              // 处理设备下的所有点位
              if (device.points && Array.isArray(device.points)) {
                deviceNode.points = device.points.map((point: any) => {
                  // 初始化address和dataType
                  let address = '';
                  let dataType = '';
                  
                  // 检查protocol是否为数组格式
                  if(point.protocol && Array.isArray(point.protocol)) {
                    // 新格式：遍历protocol数组
                    point.protocol.forEach((param: any) => {
                      if(param.level === 3) {
                        address = param.value || '';
                      } else if(param.level === 4) {
                        dataType = param.value || '';
                      }
                    });
                  } else if(point.protocol && typeof point.protocol === 'object') {
                    // 旧格式：遍历protocol对象
                    Object.entries(point.protocol).forEach(([key, value]) => {
                      // 查找对应的协议模板
                      const template = protocolTemplates.value.find(t => t.name === prototypeNode.name);
                      if(template && template.point) {
                        // 根据level判断字段类型
                        const pointConfig = template.point.find((p: any) => p.name === key);
                        if(pointConfig) {
                          if(pointConfig.level === 3) {
                            address = value as string;
                          } else if(pointConfig.level === 4) {
                            dataType = value as string;
                          }
                        }
                      }
                    });
                  }
                  
                  return {
                    id: point.id || generateUUID(),
                    code: point.code || '',
                    name: point.name || '',
                    address: address,
                    type: dataType,
                    protocol: point.protocol || []  // 保留原始protocol对象/数组
                  };
                });
              }
              
              // 添加设备到实例的子节点
              instanceNode.children.push(deviceNode);
            });
          }
          
          // 添加实例到原型的子节点
          prototypeNode.children.push(instanceNode);
        });
      }
      
      // 添加协议类型到列表
      newProtocolList.push(prototypeNode);
    });
    
    console.log('解析完成，协议列表:', newProtocolList);
    
    // 更新协议列表
    protocolList.value = newProtocolList;
  } catch (error) {
    console.error('解析组态模板出错:', error);
    ElMessage.error('解析组态模板失败');
    protocolList.value = [];
  }
};

// 处理模板选择变化 - 完全重构版本
const handleTemplateChange = (templateId: string) => {
  // 检查是否有未保存的修改
  if (isConfigModified.value || checkUnsavedChanges()) {
    ElMessageBox.confirm(
      '当前有未保存的修改，切换模板将丢失这些修改，是否继续？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
      .then(() => {
        // 用户确认切换，继续执行
        // 还原所有模板名称到原始状态
        if (originalConfig.value) {
          restoreTemplateNames();
        }
        
        // 检查当前模板是否为新建但未保存的模板
        const currentTemplate = configTemplates.value.find(t => t.id === configurationId.value);
        const isNewUnsavedTemplate = currentTemplate && 
          !originalTemplates.value.some(t => t.id === currentTemplate.id);
        
        // 如果是新建但未保存的模板，从列表中删除
        if (isNewUnsavedTemplate) {
          // 从模板列表中删除未保存的模板
          configTemplates.value = configTemplates.value.filter(t => t.id !== configurationId.value);
          console.log('删除未保存模板后的模板列表:', configTemplates.value);
        }
        
        // 加载用户选择的模板
        loadSelectedTemplate(templateId);
      })
      .catch(() => {
        // 用户取消切换，恢复选择
        selectedTemplateId.value = configurationId.value;
      });
  } else {
    // 没有未保存的修改，直接执行切换
    loadSelectedTemplate(templateId);
  }
};

// 还原模板名称到原始状态
const restoreTemplateNames = () => {
  if (originalTemplates.value.length === 0) return;
  
  // 遍历当前模板列表，还原名称
  configTemplates.value.forEach((template) => {
    // 在原始模板列表中查找对应的模板
    const originalTemplate = originalTemplates.value.find(orig => orig.id === template.id);
    if (originalTemplate) {
      // 还原模板名称
      template.name = originalTemplate.name;
    }
  });
};

// 加载选中的模板
const loadSelectedTemplate = (templateId: string) => {
  // 查找选中的模板
  const selectedTemplate = configTemplates.value.find(template => template.id === templateId);
  
  if (selectedTemplate) {
    // 设置组态ID
    configurationId.value = selectedTemplate.id;
    
    // 解析组态数据，构建协议树
    parseConfigurationTemplate(selectedTemplate);
    
    // 确保当前选中的模板ID与下拉框选中值一致
    selectedTemplateId.value = templateId;
    
    // 更新原始配置，重置修改状态
    saveOriginalConfig();
    isConfigModified.value = false;
    
    ElMessage.success(`已切换到 ${selectedTemplate.name}`);
  } else {
    ElMessage.error('未找到选中的组态');
  }
};

// 添加一个标志变量
const hasInitialized = ref(false);

// 初始化数据
onMounted(() => {
  // 防止重复初始化
  if (!hasInitialized.value) {
    // 获取组态配置模板
    fetchConfigurationTemplate();

    // 获取组态配置链路模板
    fetchConfigLinkTemplate();
    
    // 获取设备模板列表
    fetchDeviceTemplates();
    
    // 标记为已初始化
    hasInitialized.value = true;
  }
  if(currentEnv.value === 'iEMS-COM4'){
    fetchIEMSConfigInfo()
  }
  
  // 添加全局点击事件监听器，确保任何点击都能关闭右键菜单
  document.addEventListener('click', () => {
    contextMenu.visible = false;
    nodeContextMenu.visible = false;
    instanceContextMenu.visible = false;
    deviceContextMenu.visible = false;
    pointContextMenu.visible = false;
  });
});

// 在组件卸载时移除全局事件监听器
onBeforeUnmount(() => {
  document.removeEventListener('click', () => {
    contextMenu.visible = false;
    nodeContextMenu.visible = false;
    instanceContextMenu.visible = false;
    deviceContextMenu.visible = false;
    pointContextMenu.visible = false;
  });
});

// 页面离开前检查未保存的修改
onBeforeRouteLeave((to, from, next) => {
  if (isConfigModified.value || checkUnsavedChanges()) {
    ElMessageBox.confirm(
      '当前有未保存的修改，离开页面将丢失这些修改，是否继续？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
      .then(() => {
        // 还原所有模板名称到原始状态
        restoreTemplateNames();
        next();
      })
      .catch(() => {
        next(false);
      });
  } else {
    next();
  }
});

// 处理节点点击
const handleNodeClick = (data: any) => {
  // 隐藏所有右键菜单
  contextMenu.visible = false;
  nodeContextMenu.visible = false;
  instanceContextMenu.visible = false;
  deviceContextMenu.visible = false;
  pointContextMenu.visible = false;
  
  currentProtocol.value = data;
  
  // 如果是设备节点，加载点位列表
  if (data.type === 'device') {
    // 显示该设备的点位列表
    pointsList.value = data.points || [];
  } else {
    // 非设备节点，清空点位列表
    pointsList.value = [];
  }
  
  // 确保树节点高亮
  nextTick(() => {
    if (protocolTreeRef.value) {
      protocolTreeRef.value.setCurrentKey(data.id);
    }
  });
};

// 从组态同步
const syncFromConfig = () => {
  // 检查是否有未保存的修改
  if (isConfigModified.value || checkUnsavedChanges()) {
    ElMessageBox.confirm(
      '当前有未保存的修改，同步将丢失这些修改，是否继续？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
      .then(() => {
        // 用户确认同步，继续执行
        performSync();
      })
      .catch(() => {
        // 用户取消同步
      });
  } else {
    // 没有未保存的修改，直接执行同步
    performSync();
  }
};

// 执行同步操作
const performSync = () => {
  // TODO: 实现从组态同步的逻辑
  ElMessage.success('从组态同步成功');
  
  // 同步后更新原始配置
  saveOriginalConfig();
  isConfigModified.value = false;
};

// 将组态数据转换为指定结构的JSON
const convertToConfigJSON = () => {
  // 创建结果对象，添加id字段
  const result: { id: string, name: string, link: { [key: string]: any } } = {
    id: selectedTemplateId.value, // 使用当前选中的模板ID
    name: "", // 将在下面设置正确的名称
    link: {}
  };
  
  // 查找当前选中的模板，获取正确的名称
  const currentTemplate = configTemplates.value.find(template => template.id === selectedTemplateId.value);
  if (currentTemplate) {
    result.name = currentTemplate.name; // 使用当前模板的名称
  } else {
    result.name = "template"; // 如果找不到模板，使用默认名称
  }
  
  // 遍历协议列表，构建link对象
  protocolList.value.forEach(protocol => {
    if (protocol.type === 'prototype') {
      // 创建协议类型对象
      result.link[protocol.name] = {
        driverFile: protocol.driverFile || {}, // 保持 driverFile 对象
        instances: [] // 使用 instances 数组存储实例
      };
      
      // 处理该协议类型下的所有实例
      if (protocol.children && Array.isArray(protocol.children)) {
        result.link[protocol.name].instances = protocol.children.map((instance: any) => {
          // 创建实例对象
          const instanceObj: any = {
            id: instance.id,
            name: instance.name,
            protocol: instance.protocol || {},
            devices: []
          };
          
          // 处理该实例下的所有设备
          if (instance.children && Array.isArray(instance.children)) {
            instanceObj.devices = instance.children.map((device: any) => {
              // 创建设备对象
              const deviceObj: any = {
                id: device.id,
                name: device.name,
                protocol: device.protocol || {},
                points: []
              };
              
              // 处理该设备下的所有点位
              if (device.points && Array.isArray(device.points)) {
                let pointType:any = 'I'
                deviceObj.points = device.points.map((point: any) => {
                  // 查找对应的协议模板
                  const template = protocolTemplates.value.find(t => t.name === protocol.name);
                  if(!template || !template.point) {
                    return {
                      id: point.id || '',
                      code: point.code || '',
                      name: point.name || '',
                      protocol: []
                    };
                  }
                  const pattern = /^(0|4)/; // 或者 /^[04]/
                  if(protocol && (protocol.name === 'ModbusRTU(Build-202316)' || protocol.name === 'ModbusTCP(Build-202316)')){
                    if(point && point.address && pattern.test(point.address)){
                      pointType = 'IO'
                    }
                  }
                  // 构建protocol数组
                  const protocolArr: any = [];
                  
                  // 遍历模板中的point配置
                  template.point.forEach((pointConfig: any) => {
                    let value = '';
                    if(pointConfig.level === 3) {
                      // 地址字段
                      value = point.address || '';
                    } else if(pointConfig.level === 4) {
                      // 数据类型字段
                      value = point.type || '';
                    }
                    
                    protocolArr.push({
                      label: pointConfig.label || pointConfig.name,
                      name: pointConfig.name,
                      defaultValue: pointConfig.defaultValue || '',
                      value: value,
                      level: pointConfig.level,
                      inputType: pointConfig.inputType || 'input', // 保留inputType字段
                      items: pointConfig.items || [], // 保留items字段
                      visible: pointConfig.visible !== undefined ? pointConfig.visible : true // 保留visible字段
                    });
                  });
                  
                  return {
                    id: point.id || '',
                    code: point.code || '',
                    name: point.name || '',
                    pointType,
                    protocol: protocolArr
                  };
                });
              }
              
              return deviceObj;
            });
          }
          
          return instanceObj;
        });
      }
    }
  });
  
  return result;
};

// 保存配置
const saveConfiguration = async () => {
  let beforeSelectedTemplateId:any
  // 检查是否有组态模板可保存
  if (!configTemplates.value || configTemplates.value.length === 0) {
    ElMessage.warning('没有可用的组态模板，请先创建组态模板');
    return;
  }
  
  // 检查当前是否有选中的组态模板
  if (!selectedTemplateId.value) {
    ElMessage.warning('请先选择一个组态模板');
    return;
  }
  beforeSelectedTemplateId = selectedTemplateId.value
  try {
    // 显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: '保存组态配置...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    // 将组态数据转换为指定结构的JSON，包含当前选中的模板ID
    const configData = convertToConfigJSON();
    console.log('组态数据:', configData);
    
    // 调用保存配置的API，传入组态数据
    const res = await saveConfigurationTemplate(configData);
    console.log('API响应:', res);
    
    // 关闭加载状态
    loading.close();
    
    // 检查API响应
    if (res.result && res.result.resultCode === "0") {
      ElMessage.success('配置保存成功');
      
      // 保存成功后重新加载组态模板列表以同步数据
      await fetchConfigurationTemplate();
      
      // 重新选中当前模板
      if (beforeSelectedTemplateId) {
        const updatedTemplate = configTemplates.value.find(t => t.id === beforeSelectedTemplateId);
        if (updatedTemplate) {
          // 重新解析选中的模板
          parseConfigurationTemplate(updatedTemplate);
          console.log('重新加载模板后的协议列表:', protocolList.value);
        }
        selectedTemplateId.value = beforeSelectedTemplateId
      }
      
      // 保存后更新原始模板列表
      originalTemplates.value = JSON.parse(JSON.stringify(configTemplates.value));
      // 更新原始配置，确保checkUnsavedChanges()返回false
      saveOriginalConfig();
      isConfigModified.value = false;
      
      // 更新configurationId为当前选中的模板ID
      configurationId.value = selectedTemplateId.value;
    } else {
      // 如果code不是"0"，则表示失败
      ElMessage.error(`保存失败: ${res.result?.resultError || '未知错误'}`);
    }
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('配置保存失败');
  }
};

// 组态生效
const applyConfiguration = async () => {
  // 检查是否有选中的组态模板
  if (!selectedTemplateId.value) {
    ElMessage.warning('请先选择一个组态模板');
    return;
  }
  
  try {
    // 先检查运行状态
    const stateRes = await getConfigRunningState();
    if (stateRes.result && stateRes.result.resultCode === "0") {
      if (stateRes.data && stateRes.data.state === true) {
        ElMessage.warning('请先停止数据采集');
        return;
      }
    } else {
      ElMessage.error(`获取运行状态失败: ${stateRes.result?.resultError || '未知错误'}`);
      return;
    }

    // 显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: '组态生效中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    // 将组态数据转换为指定结构的JSON
    const configData = convertToConfigJSON();
    
    // 调用组态生效的API，传入组态数据
    const res = await applyLocalConfiguration(configData);
    
    // 关闭加载状态
    loading.close();
    
    // 检查API响应
    if (res.result && res.result.resultCode === "0") {
      ElMessage.success('组态生效成功');
    } else {
      // 如果code不是"0"，则表示失败
      ElMessage.error(`组态生效失败: ${res.result?.resultError || '未知错误'}`);
    }
  } catch (error) {
    console.error('组态生效失败:', error);
    ElMessage.error('组态生效失败');
  }
};

// 导出配置
const exportConfig = () => {
  try {
    // 将组态数据转换为指定结构的JSON，与保存配置使用相同的转换函数
    const configData = convertToConfigJSON();
    
    // 创建Blob对象并下载
    const blob = new Blob([JSON.stringify(configData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `组态配置_${configData.name}_${configData.id}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    ElMessage.success('配置导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('配置导出失败');
  }
};

// 导入配置
const importConfig = () => {
  // 创建文件选择器
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  input.onchange = async (event: any) => {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          // 获取文件内容
          const content = e.target?.result as string;
          console.log('原始文件内容:', content);
          
          // 解析JSON
          const importData = JSON.parse(content);
          console.log('解析后的数据:', importData);
          
          // 基本验证
          if (!importData || typeof importData !== 'object') {
            ElMessage.error('导入失败: 无效的JSON格式');
            return;
          }
          
          if (!importData.id || !importData.name) {
            ElMessage.error('导入失败: 无效的配置文件格式，缺少id或name字段');
            return;
          }
          
          if (!importData.link || typeof importData.link !== 'object') {
            ElMessage.error('导入失败: 无效的配置文件格式，缺少link字段或格式不正确');
            return;
          }
          
          // 检查每个协议类型是否包含必要的字段
          for (const protocolType in importData.link) {
            const protocol = importData.link[protocolType];
            if (!protocol.driverFile || !protocol.instances || !Array.isArray(protocol.instances)) {
              ElMessage.error(`导入失败: 协议类型 "${protocolType}" 缺少必要的字段或格式不正确`);
              return;
            }
          }
          
          // 检查是否已存在相同ID的模板
          const existingTemplate = configTemplates.value.find(
            template => template.id === importData.id
          );
          
          let confirmMessage = '';
          if (existingTemplate) {
            confirmMessage = `导入的模板 "${importData.name}" 与现有模板ID相同，将更新现有模板。是否继续？`;
          } else {
            confirmMessage = `确定要导入模板 "${importData.name}" 吗？`;
          }
          
          // 显示确认对话框
          ElMessageBox.confirm(
            confirmMessage,
            '导入确认',
            {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(() => {
            // 检查是否已存在相同ID的模板
            const existingTemplateIndex = configTemplates.value.findIndex(
              template => template.id === importData.id
            );
            
            if (existingTemplateIndex >= 0) {
              // 如果存在相同ID的模板，更新它
              configTemplates.value[existingTemplateIndex] = {
                ...importData // 直接使用导入的完整数据
              };
              ElMessage.success(`更新了现有模板: ${importData.name}`);
            } else {
              // 如果不存在，添加新模板
              configTemplates.value.push({
                ...importData // 直接使用导入的完整数据
              });
              ElMessage.success(`导入了新模板: ${importData.name}`);
            }
            
            // 更新当前选中的模板ID
            selectedTemplateId.value = importData.id;
            configurationId.value = importData.id;
            
            // 解析组态数据，构建协议树
            parseConfigurationTemplate(importData);
            
            // 标记配置已修改
            markConfigModified();
            
            // 确保UI更新
            nextTick(() => {
              // 如果有树引用，展开所有节点
              if (protocolTreeRef.value) {
                // 展开所有节点
                expandAllNodes();
              }
            });
            
            ElMessage.success('模板导入成功');
            let flag:any = false
            if(currentProtocol.value) {
              for(let i = 0;i<protocolList.value.length;i++){
                for(let j = 0;j<protocolList.value[i].children.length;j++){
                  for(let k =0;k<protocolList.value[i].children[j].children.length;k++){
                    if(protocolList.value[i].children[j].children[k].id === currentProtocol.value.id){
                      flag = true
                      handleNodeClick(protocolList.value[i].children[j].children[k])
                      break
                    }
                  }
                }
              }
              if(!flag) currentProtocol.value = ''
            }
          }).catch(() => {
            ElMessage.info('已取消导入');
          });
        } catch (error) {
          console.error('解析JSON失败:', error);
          ElMessage.error('导入失败: 无效的JSON格式');
        }
      };
      reader.readAsText(file);
    } catch (error) {
      console.error('导入失败:', error);
      ElMessage.error('模板导入失败');
    }
  };
  input.click();
};

// 添加一个辅助函数来展开所有节点
const expandAllNodes = () => {
  if (!protocolTreeRef.value) return;
  
  // 获取所有节点的keys
  const keys: any = [];
  const getKeys = (data: any[]) => {
    data.forEach(node => {
      keys.push(node.id);
      if (node.children && node.children.length > 0) {
        getKeys(node.children);
      }
    });
  };
  
  getKeys(protocolList.value);
  
  // 展开所有节点
  keys.forEach((key: any) => {
    protocolTreeRef.value!.store.nodesMap[key].expanded = true;
  });
};

// 标记配置已修改
const markConfigModified = () => {
  isConfigModified.value = true;
};

// 处理空白区域右键菜单
const handleContextMenu = (event: MouseEvent) => {
  event.preventDefault();
  
  // 隐藏其他右键菜单
  nodeContextMenu.visible = false;
  instanceContextMenu.visible = false;
  deviceContextMenu.visible = false;
  pointContextMenu.visible = false;
  
  // 显示空白区域右键菜单
  contextMenu.x = event.clientX;
  contextMenu.y = event.clientY;
  contextMenu.visible = true;
};

// 处理节点右键菜单
const handleNodeContextMenu = (event: MouseEvent, data: any, node: any) => {
  event.preventDefault();
  event.stopPropagation();
  
  // 隐藏所有右键菜单
  contextMenu.visible = false;
  nodeContextMenu.visible = false;
  instanceContextMenu.visible = false;
  deviceContextMenu.visible = false;
  pointContextMenu.visible = false;
  
  // 设置当前选中节点，实现右键时也高亮显示
  currentProtocol.value = data;
  
  // 如果是设备节点，加载点位列表
  if (data.type === 'device') {
    pointsList.value = data.points || [];
  } else {
    pointsList.value = [];
  }
  
  // 确保树节点高亮
  nextTick(() => {
    if (protocolTreeRef.value) {
      protocolTreeRef.value.setCurrentKey(data.id);
    }
  });
  
  if (node.level === 1) {
    // 原型节点
    nodeContextMenu.x = event.clientX;
    nodeContextMenu.y = event.clientY;
    nodeContextMenu.node = data;
    nodeContextMenu.visible = true;
  } else if (node.level === 2) {
    // 实例节点
    instanceContextMenu.x = event.clientX;
    instanceContextMenu.y = event.clientY;
    instanceContextMenu.node = data;
    instanceContextMenu.visible = true;
  } else if (node.level === 3) {
    // 设备节点
    deviceContextMenu.x = event.clientX;
    deviceContextMenu.y = event.clientY;
    deviceContextMenu.node = data;
    deviceContextMenu.visible = true;
  }
};

// 处理点位右键菜单
const handlePointContextMenu = (row: any, column: any, event: any) => {
  event.preventDefault();
  event.stopPropagation();
  
  // 如果当前没有选中设备，不显示菜单
  if (!currentProtocol.value || currentProtocol.value.type !== 'device') {
    return;
  }
  
  // 隐藏所有右键菜单
  contextMenu.visible = false;
  nodeContextMenu.visible = false;
  instanceContextMenu.visible = false;
  deviceContextMenu.visible = false;
  
  // 显示点位右键菜单
  pointContextMenu.x = event.clientX;
  pointContextMenu.y = event.clientY;
  pointContextMenu.data = row; // 如果在空白处右键，row可能为null
  pointContextMenu.visible = true;
};

// 处理添加协议原型
const handleAddPrototype = () => {
  contextMenu.visible = false;
  
  // 检查是否有可用的组态模板
  if (!checkAndCreateTemplateIfNeeded('addPrototype')) {
    return;
  }
  
  prototypeForm.type = '';
  prototypeDialog.visible = true;
};

// 确认添加协议原型
const confirmAddPrototype = () => {
  if (!prototypeForm.type) {
    ElMessage.warning('请选择协议类型');
    return;
  }
  
  // 获取协议类型的显示名称
  const protocolTypeObj = protocolTypes.find(item => item.value === prototypeForm.type);
  const protocolTypeLabel = protocolTypeObj ? protocolTypeObj.label : prototypeForm.type;
  
  // 检查是否已存在相同类型的协议原型
  const existingPrototype = protocolList.value.find(item => 
    item.name === protocolTypeLabel || item.protocolType === prototypeForm.type
  );
  
  if (existingPrototype) {
    ElMessage.warning(`已存在 "${protocolTypeLabel}" 类型的协议原型，请勿重复添加`);
    return;
  }
  
  // 查找对应的协议模板
  const protoTemplate = protocolTemplates.value.find(t => t.name === protocolTypeLabel);
  
  // 创建新的协议原型对象，使用8位随机ID
  const newPrototype = {
    id: generateRandomId(), // 使用8位随机ID
    name: protocolTypeLabel,
    type: 'prototype',
    protocolType: prototypeForm.type,
    driverFile: protoTemplate ? protoTemplate.driverFile : {}, // 添加 driverFile 字段
    children: []
  };
  
  // 添加到协议列表
  protocolList.value.push(newPrototype);
  prototypeDialog.visible = false;
  ElMessage.success(`添加${protocolTypeLabel}协议原型成功`);
  
  // 标记配置已修改
  markConfigModified();
  
  // 可选：设置当前选中节点为新添加的原型
  nextTick(() => {
    if (protocolTreeRef.value) {
      protocolTreeRef.value.setCurrentKey(newPrototype.id);
    }
  });
};

// 处理添加协议实例
const handleAddInstance = () => {
  nodeContextMenu.visible = false;
  
  // 检查是否有可用的组态模板
  if (!checkAndCreateTemplateIfNeeded('addInstance')) {
    return;
  }
  
  if (!nodeContextMenu.node) {
    ElMessage.warning('请先选择一个协议原型');
    return;
  }
  
  // 清空表单并设置原型信息
  instanceForm.name = '';
  instanceForm.parentId = nodeContextMenu.node.id;
  instanceForm.prototypeName = nodeContextMenu.node.name;
  instanceForm.protocol = []; // 清空之前的协议参数
  
  // 查找对应的协议模板
  const template = protocolTemplates.value.find(t => t.name === nodeContextMenu.node.name);
  
  if (template && template.channel && Array.isArray(template.channel)) {
    // 设置channel参数，复制模板中的参数并添加value字段，使用defaultValue作为初始值
    instanceForm.channelParams = template.channel.map((param: any) => {
      if(param.label === "串口" && param.useIEMSCom){
        let comList = []
        if(protocolList.value){
          for(let i =0;i<protocolList.value.length;i++){
            if(protocolList.value[i].children){
              for(let j =0;j<protocolList.value[i].children.length;j++){
                if(protocolList.value[i].children[j].protocol){
                  for(let k =0;k<protocolList.value[i].children[j].protocol.length;k++){
                    if(protocolList.value[i].children[j].protocol[k].label === "串口"){
                      comList.push(protocolList.value[i].children[j].protocol[k].value)
                    }
                  }
                }
              }
            }
          }
        }
        const items = []
        const type = template.iemsProtocolInterfaceType
        if(IEMSConfigInfo.value && IEMSConfigInfo.value.config && IEMSConfigInfo.value.config.com_link_param){
          for(let i =0;i<IEMSConfigInfo.value.config.com_link_param.length;i++){
            if(IEMSConfigInfo.value.config.com_link_param[i].type === type){
              if(comList.indexOf(IEMSConfigInfo.value.config.com_link_param[i].tty_channel) === -1){
                items.push({
                  text:IEMSConfigInfo.value.config.com_link_param[i].com_name,
                  value:IEMSConfigInfo.value.config.com_link_param[i].tty_channel,
                })
              }          
            }
          }
        }
        return {
          label: param.label,
          name: param.name,
          defaultValue: '',
          value: '',
          level: param.level, // 保留level字段
          inputType: param.inputType || 'input', // 保留inputType字段
          items: items, // 保留items字段，用于select类型的选项
          visible: param.visible !== undefined ? param.visible : true // 保留visible字段，默认为true
        }
      }
      else{
        return {
          label: param.label,
          name: param.name,
          defaultValue: param.defaultValue || '',
          value: param.defaultValue || '',
          level: param.level, // 保留level字段
          inputType: param.inputType || 'input', // 保留inputType字段
          items: param.items || [], // 保留items字段，用于select类型的选项
          visible: param.visible !== undefined ? param.visible : true // 保留visible字段，默认为true
        }
      }  
    })
    console.log('加载协议参数:', instanceForm.channelParams);
  } else {
    // 如果没有找到模板或没有channel参数，设置为空数组
    instanceForm.channelParams = [];
    console.warn('未找到协议模板或模板没有channel参数');
  }
  
  instanceDialog.visible = true;
};

// 确认添加协议实例
const confirmAddInstance = () => {
  if (!instanceForm.name) {
    ElMessage.warning('请输入实例名称');
    return;
  }
  
  // 查找父节点
  const parentNode = protocolList.value.find(item => item.id === instanceForm.parentId);
  
  if (!parentNode) {
    ElMessage.error('未找到对应的协议原型');
    return;
  }
  
  // 检查同一协议原型下是否已存在相同名称的实例
  const existingInstance = parentNode.children && parentNode.children.some((instance: any) => 
    instance.name === instanceForm.name
  );
  
  if (existingInstance) {
    ElMessage.warning(`协议原型 "${parentNode.name}" 下已存在名为 "${instanceForm.name}" 的实例`);
    return;
  }
  
  // 准备协议参数，保持原始结构并添加value
  const protocolParams = instanceForm.channelParams.map((param: any) => ({
    label: param.label,
    name: param.name,
    defaultValue: param.defaultValue || '',
    value: param.value || '',
    level: param.level, // 保留level字段
    inputType: param.inputType || 'input', // 保留inputType字段
    items: param.items || [], // 保留items字段
    visible: param.visible !== undefined ? param.visible : true // 保留visible字段
  }));
  
  // 创建新的实例节点
  const newInstance = {
    id: generateUUID(),
    name: instanceForm.name,
    protocol: protocolParams,
    children: [],
    type: 'instance'
  };
  
  // 添加到父节点的children数组
  if (!parentNode.children) {
    parentNode.children = [];
  }
  parentNode.children.push(newInstance);
  
  instanceDialog.visible = false;
  ElMessage.success(`添加协议实例 "${instanceForm.name}" 成功`);
  
  // 标记配置已修改
  markConfigModified();
  
  // 设置当前选中节点为新添加的实例
  nextTick(() => {
    if (protocolTreeRef.value) {
      protocolTreeRef.value.setCurrentKey(newInstance.id);
    }
  });
};

// 处理删除协议原型
const handleDeletePrototype = () => {
  nodeContextMenu.visible = false;
  
  if (!nodeContextMenu.node) {
    ElMessage.warning('请先选择一个协议原型');
    return;
  }
  
  // 二次确认
  ElMessageBox.confirm(`确定要删除协议原型 "${nodeContextMenu.node.name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 从协议列表中删除
      protocolList.value = protocolList.value.filter(item => item.id !== nodeContextMenu.node.id);
      
      // 如果当前选中的是被删除的节点，清空当前选中
      if (currentProtocol.value && currentProtocol.value.id === nodeContextMenu.node.id) {
        currentProtocol.value = null;
        pointsList.value = [];
      }
      
      // 标记配置已修改
      markConfigModified();
      
      ElMessage.success(`删除协议原型 "${nodeContextMenu.node.name}" 成功`);
    })
    .catch(() => {
      // 取消删除
    });
};

// 处理添加设备
const handleAddDevice = () => {
  instanceContextMenu.visible = false;
  const count:any = {
    'rs485':0,
    'rs232':0,
    'tcp':0
  }
  for(let i =0;i<protocolList.value.length;i++){
    const template = protocolTemplates.value.find(t => t.name === protocolList.value[i].name);
    const type = template.iemsProtocolInterfaceType
    if(protocolList.value[i].children){
      for(let j=0;j<protocolList.value[i].children.length;j++){
        if(protocolList.value[i].children[j].children) {
          count[type] = count[type] + protocolList.value[i].children[j].children.length
        }
      }
    }
  }
  const nameObj = protocolList.value.find(t => {
    for(let i =0;i<t.children.length;i++){
      if(t.children[i].id === instanceContextMenu.node.id){
        return t
      }       
    }
  })
  const template = protocolTemplates.value.find(t =>  t.name === nameObj.name);
  const type = template?.iemsProtocolInterfaceType
  if(IEMSConfigInfo.value && IEMSConfigInfo.value.config && IEMSConfigInfo.value.config.slave_max_limit && count[type] === IEMSConfigInfo.value.config.slave_max_limit[type]){
    ElMessage.error(`${type}从站数已经达到最大值${IEMSConfigInfo.value.config.slave_max_limit[type]}`)
    return
  }
  // console.log(protocolList.value,'protocolList.value')
  // 检查是否有可用的组态模板
  if (!checkAndCreateTemplateIfNeeded('addDevice')) {
    return;
  }
  
  if (!instanceContextMenu.node) {
    ElMessage.warning('请先选择一个协议实例');
    return;
  }
  
  // 清空表单并设置实例信息
  deviceForm.name = '';
  deviceForm.parentId = instanceContextMenu.node.id;
  deviceForm.instanceName = instanceContextMenu.node.name;
  deviceForm.protocol = []; // 清空之前的协议参数
  
  // 查找对应的协议原型
  let prototypeNode: any = null;
  for (const prototype of protocolList.value) {
    if (prototype.children) {
      const instance = prototype.children.find((inst: any) => inst.id === instanceContextMenu.node.id);
      if (instance) {
        prototypeNode = prototype;
        break;
      }
    }
  }
  
  if (prototypeNode) {
    // 查找对应的协议模板
    const template = protocolTemplates.value.find(t => t.name === prototypeNode.name);
    
    if (template && template.device && Array.isArray(template.device)) {
      // 设置device参数，复制模板中的参数并添加value字段，使用defaultValue作为初始值
      deviceForm.deviceParams = template.device.map((param: any) => ({
        label: param.label,
        name: param.name,
        defaultValue: param.defaultValue || '',
        value: param.defaultValue || '',
        level: param.level, // 保留level字段
        inputType: param.inputType || 'input', // 保留inputType字段
        items: param.items || [], // 保留items字段，用于select类型的选项
        visible: param.visible !== undefined ? param.visible : true // 保留visible字段，默认为true
      }));
      console.log('加载设备参数:', deviceForm.deviceParams);
    } else {
      // 如果没有找到模板或没有device参数，设置为空数组
      deviceForm.deviceParams = [];
      console.warn('未找到协议模板或模板没有device参数');
    }
  } else {
    deviceForm.deviceParams = [];
    console.warn('未找到对应的协议原型');
  }
  
  deviceDialog.visible = true;
};

// 确认添加设备
const confirmAddDevice = () => {
  if (!deviceForm.name) {
    ElMessage.warning('请输入设备名称');
    return;
  }
  
  // 查找父节点
  let parentNode = null;
  
  // 遍历协议列表查找实例节点
  for (const prototype of protocolList.value) {
    if (prototype.children) {
      parentNode = prototype.children.find((instance: any) => instance.id === deviceForm.parentId);
      if (parentNode) break;
    }
  }
  
  if (!parentNode) {
    ElMessage.error('未找到对应的协议实例');
    return;
  }
  
  // 检查同一协议实例下是否已存在相同名称的设备
  const existingDevice = parentNode.children && parentNode.children.some((device: any) => 
    device.name === deviceForm.name
  );
  
  if (existingDevice) {
    ElMessage.warning(`协议实例 "${parentNode.name}" 下已存在名为 "${deviceForm.name}" 的设备`);
    return;
  }
  
  // 准备设备参数，保持原始结构并添加value
  const deviceProtocolParams = deviceForm.deviceParams.map((param: any) => ({
    label: param.label,
    name: param.name,
    defaultValue: param.defaultValue === 0?param.defaultValue:(param.defaultValue || ''),
    value: param.value === 0 ? param.value :(param.value || ''),
    level: param.level, // 保留level字段
    inputType: param.inputType || 'input', // 保留inputType字段
    items: param.items || [], // 保留items字段
    visible: param.visible !== undefined ? param.visible : true // 保留visible字段
  }));
  
  console.log('设备参数(添加前):', deviceProtocolParams); // 调试日志
  
  // 创建新的设备节点
  const newDevice: any = {
    id: generateUUID(),
    name: deviceForm.name,
    protocol: deviceProtocolParams,
    points: [],
    type: 'device'
  };
  
  console.log('新设备对象:', newDevice); // 调试日志
  
  // 添加到父节点的children数组
  if (!parentNode.children) {
    parentNode.children = [];
  }
  parentNode.children.push(newDevice);
  deviceDialog.visible = false;
  ElMessage.success(`添加设备 "${deviceForm.name}" 成功`);
  
  // 标记配置已修改
  markConfigModified();
  
  // 设置当前选中节点为新添加的设备
  nextTick(() => {
    if (protocolTreeRef.value) {
      protocolTreeRef.value.setCurrentKey(newDevice.id);
      
      // 更新当前选中的协议/设备
      currentProtocol.value = newDevice;
      
      // 更新点位列表
      pointsList.value = [...newDevice.points];
    }
  });
};

// 处理从设备模板添加
const handleAddDeviceFromTemplate = () => {
  instanceContextMenu.visible = false;
  const count:any = {
    'rs485':0,
    'rs232':0,
    'tcp':0
  }
  for(let i =0;i<protocolList.value.length;i++){
    const template = protocolTemplates.value.find(t => t.name === protocolList.value[i].name);
    const type = template.iemsProtocolInterfaceType
    if(protocolList.value[i].children){
      for(let j=0;j<protocolList.value[i].children.length;j++){
        if(protocolList.value[i].children[j].children) {
          count[type] = count[type] + protocolList.value[i].children[j].children.length
        }
      }
    }
  }
  const nameObj = protocolList.value.find(t => {
    for(let i =0;i<t.children.length;i++){
      if(t.children[i].id === instanceContextMenu.node.id){
        return t
      }       
    }
  })
  const template = protocolTemplates.value.find(t => t.name === nameObj.name);
  const type = template?.iemsProtocolInterfaceType
  if(IEMSConfigInfo.value && IEMSConfigInfo.value.config && IEMSConfigInfo.value.config.slave_max_limit && count[type] === IEMSConfigInfo.value.config.slave_max_limit[type]){
    ElMessage.error(`${type}从站数已经达到最大值${IEMSConfigInfo.value.config.slave_max_limit[type]}`)
    return
  }
  // 检查是否有可用的组态模板
  if (!checkAndCreateTemplateIfNeeded('addDeviceFromTemplate')) {
    return;
  }
  
  if (!instanceContextMenu.node) {
    ElMessage.warning('请先选择一个协议实例');
    return;
  }
  
  // 清空表单并设置实例信息
  templateDeviceForm.name = '';
  templateDeviceForm.parentId = instanceContextMenu.node.id;
  templateDeviceForm.instanceName = instanceContextMenu.node.name;
  templateDeviceForm.templateId = '';
  templateDeviceForm.deviceParams = []; // 确保初始化为空数组
  
  // 查找对应的协议原型
  let prototypeNode: any = null;
  for (const prototype of protocolList.value) {
    if (prototype.children) {
      const instance = prototype.children.find((inst: any) => inst.id === instanceContextMenu.node.id);
      if (instance) {
        prototypeNode = prototype;
        break;
      }
    }
  }
  
  if (prototypeNode) {
    // 查找对应的协议模板
    const template = protocolTemplates.value.find(t => t.name === prototypeNode.name);
    
    if (template && template.device && Array.isArray(template.device)) {
      // 设置device参数，复制模板中的参数并添加value字段，使用defaultValue作为初始值
      templateDeviceForm.deviceParams = template.device.map((param: any) => ({
        label: param.label,
        name: param.name,
        defaultValue: param.defaultValue || '',
        value: param.defaultValue || '',
        level: param.level, // 保留level字段
        inputType: param.inputType || 'input', // 保留inputType字段
        items: param.items || [], // 保留items字段，用于select类型的选项
        visible: param.visible !== undefined ? param.visible : true // 保留visible字段，默认为true
      }));
      console.log('加载设备参数(模板):', templateDeviceForm.deviceParams);
    } else {
      // 如果没有找到模板或没有device参数，设置为空数组
      templateDeviceForm.deviceParams = [];
      console.warn('未找到协议模板或模板没有device参数');
    }
  } else {
    templateDeviceForm.deviceParams = [];
    console.warn('未找到对应的协议原型');
  }
  
  // 获取设备模板列表
  fetchDeviceTemplates();
  
  // 显示对话框
  templateDeviceDialog.visible = true;
};

// 确认从模板添加设备
const confirmAddTemplateDevice = () => {
  if (!templateDeviceForm.name) {
    ElMessage.warning('请输入设备名称');
    return;
  }
  
  if (!templateDeviceForm.templateId) {
    ElMessage.warning('请选择设备模板');
    return;
  }
  
  // 查找父节点
  let parentNode = null;
  
  // 遍历协议列表查找实例节点
  for (const prototype of protocolList.value) {
    if (prototype.children) {
      parentNode = prototype.children.find((instance: any) => instance.id === templateDeviceForm.parentId);
      if (parentNode) break;
    }
  }
  
  if (!parentNode) {
    ElMessage.error('未找到对应的协议实例');
    return;
  }
  
  // 检查同一协议实例下是否已存在相同名称的设备
  const existingDevice = parentNode.children && parentNode.children.some((device: any) => 
    device.name === templateDeviceForm.name
  );
  
  if (existingDevice) {
    ElMessage.warning(`协议实例 "${parentNode.name}" 下已存在名为 "${templateDeviceForm.name}" 的设备`);
    return;
  }
  
  // 查找设备模板
  const template = deviceTemplates.value.find(item => item.id === templateDeviceForm.templateId);
  
  if (!template) {
    ElMessage.error('未找到对应的设备模板');
    return;
  }
  
  // 获取当前协议实例的协议类型
  let currentProtocolType = '';
  for (const prototype of protocolList.value) {
    if (prototype.children) {
      const instance = prototype.children.find((inst: any) => inst.id === templateDeviceForm.parentId);
      if (instance) {
        currentProtocolType = prototype.name; // 协议原型的名称就是协议类型
        break;
      }
    }
  }
  
  console.log('当前协议类型:', currentProtocolType); // 调试日志
  
  // 处理设备模板中的点位，根据协议类型匹配决定是否使用数据类型
  const processedPoints = template.points ? template.points.map((point: any) => {
    const newPoint = {
      id: point.id,
      code: point.code,
      name: point.name,
      description: point.description,
      address: point.address,
      type: '', // 默认为空
      protocolType: point.protocolType || ''
    };
    
    // 如果当前选中的协议类型与点位上的协议类型相同，则使用点位上的数据类型
    if (currentProtocolType && point.protocolType && currentProtocolType === point.protocolType) {
      newPoint.type = point.type || '';
      console.log(`点位 ${point.code} 协议类型匹配 (${currentProtocolType})，使用数据类型: ${point.type}`);
    } else {
      console.log(`点位 ${point.code} 协议类型不匹配 (当前: ${currentProtocolType}, 点位: ${point.protocolType})，数据类型设为空`);
    }
    
    return newPoint;
  }) : [];
  
  console.log('处理后的点位列表:', processedPoints); // 调试日志
  
  // 准备设备参数，保持原始结构并添加value
  // 注意：这里需要检查templateDeviceForm.deviceParams是否存在
  const deviceProtocolParams = templateDeviceForm.deviceParams ? templateDeviceForm.deviceParams.map((param: any) => ({
    label: param.label,
    name: param.name,
    defaultValue: param.defaultValue || '',
    value: param.value || '',
    level: param.level, // 保留level字段
    inputType: param.inputType || 'input', // 保留inputType字段
    items: param.items || [], // 保留items字段
    visible: param.visible !== undefined ? param.visible : true // 保留visible字段
  })) : [];
  
  console.log('设备参数(模板添加前):', deviceProtocolParams); // 调试日志
  
  // 创建新的设备对象，包含处理后的点位
  const newDevice = {
    id: generateRandomId(), // 使用8位随机ID
    name: templateDeviceForm.name,
    type: 'device',
    parentId: templateDeviceForm.parentId,
    templateId: templateDeviceForm.templateId,
    protocol: deviceProtocolParams, // 保存用户填写的设备参数
    points: processedPoints // 使用处理后的点位列表
  };
  
  console.log('新设备对象(从模板):', newDevice); // 调试日志
  
  // 添加到父节点的子节点列表
  if (!parentNode.children) {
    parentNode.children = [];
  }
  parentNode.children.push(newDevice);
  templateDeviceDialog.visible = false;
  ElMessage.success(`从模板添加设备 "${templateDeviceForm.name}" 成功`);
  
  // 标记配置已修改
  markConfigModified();
  
  // 设置当前选中节点为新添加的设备
  nextTick(() => {
    if (protocolTreeRef.value) {
      protocolTreeRef.value.setCurrentKey(newDevice.id);
      
      // 更新当前选中的协议/设备
      currentProtocol.value = newDevice;
      
      // 更新点位列表
      pointsList.value = [...newDevice.points];
    }
  });
};

// 处理删除协议实例
const handleDeleteInstance = () => {
  instanceContextMenu.visible = false;
  
  if (!instanceContextMenu.node) {
    ElMessage.warning('请先选择一个协议实例');
    return;
  }
  
  // 二次确认
  ElMessageBox.confirm(`确定要删除协议实例 "${instanceContextMenu.node.name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 查找父节点
      for (const prototype of protocolList.value) {
        if (prototype.children) {
          const index = prototype.children.findIndex((instance: any) => instance.id === instanceContextMenu.node.id);
          if (index !== -1) {
            // 从父节点的子节点列表中删除
            prototype.children.splice(index, 1);
            
            // 如果当前选中的是被删除的节点或其子节点，清空当前选中
            if (currentProtocol.value) {
              const isCurrentOrChild = 
                currentProtocol.value.id === instanceContextMenu.node.id || 
                currentProtocol.value.parentId === instanceContextMenu.node.id;
              
              if (isCurrentOrChild) {
                currentProtocol.value = null;
                pointsList.value = [];
              }
            }
            
            // 标记配置已修改
            markConfigModified();
            
            ElMessage.success(`删除协议实例 "${instanceContextMenu.node.name}" 成功`);
            break;
          }
        }
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 处理删除设备
const handleDeleteDevice = () => {
  deviceContextMenu.visible = false;
  
  if (!deviceContextMenu.node) {
    ElMessage.warning('请先选择一个设备');
    return;
  }
  
  // 二次确认
  ElMessageBox.confirm(`确定要删除设备 "${deviceContextMenu.node.name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 查找父节点
      for (const prototype of protocolList.value) {
        if (prototype.children) {
          for (const instance of prototype.children) {
            if (instance.children) {
              const index = instance.children.findIndex((device: any) => device.id === deviceContextMenu.node.id);
              if (index !== -1) {
                // 从实例的子节点列表中删除设备
                instance.children.splice(index, 1);
                
                // 如果当前选中的是被删除的设备，清空当前选中
                if (currentProtocol.value && currentProtocol.value.id === deviceContextMenu.node.id) {
                  currentProtocol.value = null;
                  pointsList.value = [];
                }
                
                // 标记配置已修改
                markConfigModified();
                
                ElMessage.success(`删除设备 "${deviceContextMenu.node.name}" 成功`);
                return;
              }
            }
          }
        }
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 处理新增位号
const handleAddPoint = () => {
  // 检查是否有可用的组态模板
  if (!checkAndCreateTemplateIfNeeded('addPoint')) {
    return;
  }
  
  if (!currentProtocol.value || currentProtocol.value.type !== 'device') {
    ElMessage.warning('请先选择一个设备');
    return;
  }
  
  let registerCount = 0
  for(let i =0;i<protocolList.value.length;i++){
    if(protocolList.value[i].children){
      for(let j=0;j<protocolList.value[i].children.length;j++){
        if(protocolList.value[i].children[j].children) {
          for(let k =0;k<protocolList.value[i].children[j].children.length;k++){
            if(protocolList.value[i].children[j].children[k].points) registerCount = protocolList.value[i].children[j].children[k].points.length + registerCount
          }
        }
      }
    }
  }
  if(registerCount === IEMSConfigInfo.value.config.register_max_num) {
    ElMessage.error(`位号已经达到最大值${IEMSConfigInfo.value.config.register_max_num}`)
    return
  }
  // 重置表单
  pointForm.id = '';
  pointForm.code = '';
  pointForm.name = '';
  
  // 为 required 为 false 的字段设置默认值或空值
  if (pointAddressConfig.value && pointAddressConfig.value.required === false) {
    pointForm.address = pointAddressConfig.value.defaultValue || '';
  } else {
    pointForm.address = '';
  }
  
  if (pointTypeConfig.value && pointTypeConfig.value.required === false) {
    pointForm.type = pointTypeConfig.value.defaultValue || '';
  } else {
    pointForm.type = '';
  }
  
  // 显示对话框
  pointDialog.title = '新增位号';
  pointDialog.visible = true;
};

// 处理批量新增位号
const handleBatchAddPoints = () => {
  // 检查是否有可用的组态模板
  if (!checkAndCreateTemplateIfNeeded('batchAddPoints')) {
    return;
  }
  
  if (!currentProtocol.value || currentProtocol.value.type !== 'device') {
    ElMessage.warning('请先选择一个设备');
    return;
  }
  let registerCount = 0
  for(let i =0;i<protocolList.value.length;i++){
    if(protocolList.value[i].children){
      for(let j=0;j<protocolList.value[i].children.length;j++){
        if(protocolList.value[i].children[j].children) {
          for(let k =0;k<protocolList.value[i].children[j].children.length;k++){
            if(protocolList.value[i].children[j].children[k].points) registerCount = protocolList.value[i].children[j].children[k].points.length + registerCount
          }
        }
      }
    }
  }
  if(registerCount === IEMSConfigInfo.value.config.register_max_num) {
    ElMessage.error(`位号已经达到最大值${IEMSConfigInfo.value.config.register_max_num}`)
    return
  }
  // 重置表单
  batchPointForm.startAddress = '';
  batchPointForm.registerCount = 1;
  batchPointForm.registerType = '';
  batchPointForm.namePrefix = '';
  
  // 清除预览
  previewPoints.value = [];
  hasBaseAddress.value = false
  if(currentProtocol.value && currentProtocol.value.protocol){
    for(let i =0;i<currentProtocol.value.protocol.length;i++){
      if(currentProtocol.value.protocol[i].name === "BaseAddress"){
        hasBaseAddress.value = true
      }
    }
  }
  // 显示批量添加对话框
  batchPointDialog.visible = true;
};



// 预览批量新增位号
const previewBatchPoints = () => {
  if (!batchPointFormRef.value) return;
  let registerCount = 0
  for(let i =0;i<protocolList.value.length;i++){
    if(protocolList.value[i].children){
      for(let j=0;j<protocolList.value[i].children.length;j++){
        if(protocolList.value[i].children[j].children) {
          for(let k =0;k<protocolList.value[i].children[j].children.length;k++){
            if(protocolList.value[i].children[j].children[k].points) registerCount = protocolList.value[i].children[j].children[k].points.length + registerCount
          }
        }
      }
    }
  }
  if(registerCount + batchPointForm.registerCount > IEMSConfigInfo.value.config.register_max_num) {
    ElMessage.error(`位号已经超过最大值${IEMSConfigInfo.value.config.register_max_num}，最多可建${IEMSConfigInfo.value.config.register_max_num - registerCount }个位号`)
    return
  }
  batchPointFormRef.value.validate((valid: any, fields: any) => {
    if (valid) {
      // 生成预览点位列表
      generatePreviewPoints();
    } else {
      console.log('表单验证失败:', fields);
    }
  });
};

// 生成预览位号列表
const generatePreviewPoints = () => {
  const points: any[] = [];
  const count = batchPointForm.registerCount;
  const prefix = batchPointForm.namePrefix;
  
  // 1. 先获取选择的数据类型对应的value值，统一转为字符串再转为全小写
  const dataTypeValue = String(batchPointForm.registerType || '').toLowerCase();
  
  // 2. 判断转换后的字符是否为特定的数据类型字符串
  const specificDataTypes = [
    'bool', 'boolean', 'bit', 'byte', 'sbyte', 'uint8', 'int8', 'uint16', 'int16', 
    'uint32', 'int32', 'uint64', 'int64', 'float', 'double'
  ];
  const isSpecificDataType = specificDataTypes.includes(dataTypeValue);
  
  // 解析开始地址（支持数字和字符串）
  let startAddr: number;
  if (typeof batchPointForm.startAddress === 'string' && /^\d+$/.test(batchPointForm.startAddress)) {
    startAddr = parseInt(batchPointForm.startAddress);
  } else {
    // 如果不是纯数字，直接使用字符串
    startAddr = 0; // 默认从0开始，后续可以改进支持更复杂的地址格式
  }
  
  for (let i = 0; i < count; i++) {
    // 生成位号名称，格式：前缀+序号（3位补零）
    const code = `${prefix}${String(i + 1).padStart(3, '0')}`;
    
    // 计算地址
    let address: string;
    
    if (!isSpecificDataType) {
      // 3. 如果不是特定数据类型，则按照填写的开始地址依次+1计算位号寄存器地址
      if (typeof batchPointForm.startAddress === 'string' && /^\d+$/.test(batchPointForm.startAddress)) {
        let formattedStr = (startAddr + i).toString(); // 转换为字符串，但会丢失前导零
        let fixedStr = formattedStr.padStart(batchPointForm.startAddress.length, '0'); // 使用padStart来补回前导零
        address = fixedStr;
      } else {
        // 如果开始地址不是数字，直接使用原始格式加偏移
        address = `${batchPointForm.startAddress}+${i}`;
      }
    } else {
      // 如果是特定数据类型，根据数据类型占据的寄存器大小递增
      if (typeof batchPointForm.startAddress === 'string' && /^\d+$/.test(batchPointForm.startAddress)) {
        // 根据数据类型计算地址递增规则
        if (dataTypeValue === 'bool' || dataTypeValue === 'boolean' || dataTypeValue === 'bit') {
          // bool/boolean/bit类型：按位递增，每个寄存器16位
          // const registerIndex = Math.floor(i / 16); // 寄存器索引
          // const bitIndex = i % 16; // 位索引
          let formattedStr = (startAddr + i).toString(); // 转换为字符串，但会丢失前导零
          let fixedStr = formattedStr.padStart(batchPointForm.startAddress.length, '0'); // 使用padStart来补回前导零
          address = fixedStr;
          // address = `${startAddr + registerIndex}.${bitIndex}`;
        } else {
          // 其他类型：根据位宽递增
          let registerSize: number; // 占用的寄存器数量
          let registerIndex:number; //寄存器索引
          let bitIndex:number; //位索引
          switch (dataTypeValue) {
            case 'byte':
            case 'sbyte':
            case 'int8':
            case 'uint8':
              // 8位类型，按照16位(1个寄存器)的位宽算
              registerSize = 1;
              registerIndex = Math.floor(i / 16); // 寄存器索引
              bitIndex = i % 16; // 位索引
              break;
            case 'int16':
            case 'uint16':
              // 16位类型，占1个寄存器
              registerSize = 1;
              registerIndex = Math.floor(i / 16); // 寄存器索引
              bitIndex = i % 16; // 位索引
              break;
            case 'int32':
            case 'uint32':
            case 'float':
              // 32位类型，占2个寄存器
              registerSize = 2;
              registerIndex = Math.floor(i / 32); // 寄存器索引
              bitIndex = i % 32; // 位索引
              break;
            case 'int64':
            case 'uint64':
            case 'double':
              // 64位类型，占4个寄存器
              registerSize = 4;
              registerIndex = Math.floor(i / 64); // 寄存器索引
              bitIndex = i % 64; // 位索引
              break;
            default:
              // 默认按1个寄存器计算
              registerSize = 1;
              registerIndex = Math.floor(i / 16); // 寄存器索引
              bitIndex = i % 16; // 位索引
          }          
          // address = String(startAddr + i * registerSize);
          address = `${startAddr + registerIndex}.${bitIndex}`;
        }
        let formattedStr = address.toString(); // 转换为字符串，但会丢失前导零
        let fixedStr = formattedStr.padStart(batchPointForm.startAddress.length, '0'); // 使用padStart来补回前导零
        address = fixedStr;
      } else {
        // 如果开始地址不是数字，直接使用原始格式加偏移
        address = `${batchPointForm.startAddress}+${i}`;
      }
    }
    
    points.push({
      code: code,
      address: address
    });
  }
  
  previewPoints.value = points;
};

// 清除预览
const clearPreview = () => {
  previewPoints.value = [];
};

// 取消批量添加
const cancelBatchAdd = () => {
  // 清除预览数据
  previewPoints.value = [];
  // 关闭对话框
  batchPointDialog.visible = false;
};

// 确认批量新增位号
const confirmBatchAddPoints = () => {
  // 检查是否有预览数据
  if (previewPoints.value.length === 0) {
    ElMessage.warning('请先预览位号');
    return;
  }
  
  // 检查是否选中了设备
  if (!currentProtocol.value || currentProtocol.value.type !== 'device') {
    ElMessage.warning('请先选择一个设备');
    return;
  }
  
  try {
    // 确保设备有points数组
    if (!currentProtocol.value.points) {
      currentProtocol.value.points = [];
    }
    
    // 检查是否有重复的位号名称
    const existingCodes = currentProtocol.value.points.map((p: any) => p.code);
    const duplicateCodes = previewPoints.value.filter(point => 
      existingCodes.includes(point.code)
    );
    
    if (duplicateCodes.length > 0) {
      const duplicateCodesStr = duplicateCodes.map(p => p.code).join(', ');
      ElMessage.warning(`以下位号名称已存在，请修改后重试：${duplicateCodesStr}`);
      return;
    }
    
    // 创建新的点位对象
    const newPoints = previewPoints.value.map(previewPoint => ({
      id: generateUUID(),
      code: previewPoint.code,
      name: previewPoint.code, // 点位描述与编码相同
      address: previewPoint.address,
      type: batchPointForm.registerType || '' // 使用表单中选择的数据类型
    }));
    
    // 添加到设备的点位列表
    currentProtocol.value.points.push(...newPoints);
    
    // 标记配置已修改
    markConfigModified();
    
    // 更新点位列表显示
    pointsList.value = [...currentProtocol.value.points];
    
    // 显示成功消息
    ElMessage.success(`成功添加 ${newPoints.length} 个位号`);
    
    // 关闭对话框
    batchPointDialog.visible = false;
    
    // 清除预览数据
    previewPoints.value = [];
    
    console.log('批量添加的位号:', newPoints);
    
  } catch (error) {
    console.error('批量添加位号失败:', error);
    ElMessage.error('批量添加位号失败');
  }
};

// 处理修改位号
const handleEditPoint = (row: any) => {
  // 填充表单
  pointForm.id = row.id;
  pointForm.code = row.code || '';
  pointForm.name = row.name;
  
  // 处理地址字段
  if (pointAddressConfig.value && pointAddressConfig.value.required === false) {
    // 如果字段不是必填的，使用默认值
    pointForm.address = pointAddressConfig.value.defaultValue || '';
  } else {
    pointForm.address = row.address || '';
  }
  
  // 处理类型字段
  if (pointTypeConfig.value && pointTypeConfig.value.required === false) {
    // 如果字段不是必填的，使用默认值
    pointForm.type = pointTypeConfig.value.defaultValue || '';
  } else {
    pointForm.type = row.type || '';
    
    // 如果是select类型，确保值在选项中存在
    if (pointTypeConfig.value && pointTypeConfig.value.inputType === 'select' && pointTypeConfig.value.items) {
      const matchingItem = pointTypeConfig.value.items.find((item: any) => String(item.value) === String(row.type));
      if (matchingItem) {
        pointForm.type = matchingItem.value; // 确保使用选项中的值
      }
    }
  }
  
  // 调试信息：检查数据类型配置和当前值
  console.log('编辑点位 - 原始数据:', row);
  console.log('编辑点位 - 地址配置:', pointAddressConfig.value);
  console.log('编辑点位 - 数据类型配置:', pointTypeConfig.value);
  
  pointDialog.title = '修改位号';
  pointDialog.visible = true;
};

// 确认添加/编辑点位
const confirmAddOrEditPoint = () => {
  if (!pointFormRef.value) return;
  
  pointFormRef.value.validate((valid: any, fields: any) => {
    if (valid) {
      if (!currentProtocol.value || currentProtocol.value.type !== 'device') {
        ElMessage.warning('请先选择一个设备');
        return;
      }
      
      // 确保设备有points数组
      if (!currentProtocol.value.points) {
        currentProtocol.value.points = [];
      }
      
      // 检查同一设备下是否已存在相同名称的点位（编辑模式下排除当前点位）
      const isDuplicate = currentProtocol.value.points.some((point: any) => {
        // 如果是编辑模式且是当前正在编辑的点位，则跳过
        if (pointForm.id && point.id === pointForm.id) {
          return false;
        }
        // 检查名称是否相同
        return point.code === pointForm.code;
      });
      
      if (isDuplicate) {
        ElMessage.warning(`设备 "${currentProtocol.value.name}" 下已存在名称为 "${pointForm.code}" 的点位`);
        return;
      }
      
      if (pointForm.id) {
        // 编辑现有点位
        const index = currentProtocol.value.points.findIndex((item: any) => item.id === pointForm.id);
        if (index !== -1) {
          currentProtocol.value.points[index] = {
            ...currentProtocol.value.points[index],
            code: pointForm.code,
            name: pointForm.name,
            address: pointForm.address || '',  // 确保地址为空时使用空字符串
            type: pointForm.type || ''         // 确保类型为空时使用空字符串
          };
          ElMessage.success('位号更新成功');
        }
      } else {
        // 添加新点位
        const newPoint = {
          id: Date.now().toString(),
          code: pointForm.code,
          name: pointForm.name,
          address: pointForm.address || '',    // 确保地址为空时使用空字符串
          type: pointForm.type || ''           // 确保类型为空时使用空字符串
        };
        
        currentProtocol.value.points.push(newPoint);
        ElMessage.success('位号添加成功');
      }
      
      // 标记配置已修改
      markConfigModified();
      
      // 更新点位列表
      pointsList.value = [...currentProtocol.value.points];
      
      // 关闭对话框
      pointDialog.visible = false;
    } else {
      console.log('表单验证失败:', fields);
    }
  });
};

// 处理删除位号
const handleDeletePoint = (row: any) => {
  if (!currentProtocol.value || currentProtocol.value.type !== 'device') {
    ElMessage.warning('请先选择一个设备');
    return;
  }
  
  // 二次确认
  ElMessageBox.confirm(`确定要删除位号 "${row.name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 从设备的点位列表中删除
      if (currentProtocol.value.points) {
        currentProtocol.value.points = currentProtocol.value.points.filter((item: any) => item.id !== row.id);
        
        // 标记配置已修改
        markConfigModified();
        
        // 更新点位列表
        pointsList.value = [...currentProtocol.value.points];
        
        ElMessage.success(`删除位号 "${row.name}" 成功`);
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 处理编辑协议实例
const handleEditInstance = () => {
  instanceContextMenu.visible = false;
  
  if (!instanceContextMenu.node) {
    ElMessage.warning('请先选择一个协议实例');
    return;
  }
  
  // 查找父节点（协议原型）
  const parentNode = protocolList.value.find(item => 
    item.children && item.children.some((child: any) => child.id === instanceContextMenu.node.id)
  );
  
  if (!parentNode) {
    ElMessage.error('未找到对应的协议原型');
    return;
  }
  
  // 查找实例节点
  const instanceNode = parentNode.children.find((instance: any) => instance.id === instanceContextMenu.node.id);
  
  if (!instanceNode) {
    ElMessage.error('未找到对应的协议实例');
    return;
  }
  
  // 填充表单
  editInstanceForm.id = instanceNode.id;
  editInstanceForm.name = instanceNode.name;
  editInstanceForm.prototypeName = parentNode.name;
  editInstanceForm.parentId = parentNode.id;
  
  // 添加协议参数
  editInstanceForm.protocol = instanceNode.protocol || [];
  editInstanceForm.channelParams = [];
  
  // 查找对应的协议模板
  const template = protocolTemplates.value.find(t => t.name === parentNode.name);
  
  if (template && template.channel && Array.isArray(template.channel)) {
    // 设置channel参数，结合模板和已有的参数值
    editInstanceForm.channelParams = template.channel.map((templateParam: any) => {
      // 查找实例中已有的参数
      const existingParam = instanceNode.protocol ? 
        instanceNode.protocol.find((p: any) => p.name === templateParam.name) : null;
      
      if(templateParam.label === "串口" && templateParam.useIEMSCom){
        let comList = []
        if(protocolList.value){
          for(let i =0;i<protocolList.value.length;i++){
            if(protocolList.value[i].children){
              for(let j =0;j<protocolList.value[i].children.length;j++){
                if(protocolList.value[i].children[j].protocol){
                  for(let k =0;k<protocolList.value[i].children[j].protocol.length;k++){
                    if(protocolList.value[i].children[j].protocol[k].label === "串口" && protocolList.value[i].children[j].protocol[k].value !== existingParam.value){
                      comList.push(protocolList.value[i].children[j].protocol[k].value)
                    }
                  }
                }
              }
            }
          }
        }
        const items = []
        const type = template.iemsProtocolInterfaceType
        if(IEMSConfigInfo.value && IEMSConfigInfo.value.config && IEMSConfigInfo.value.config.com_link_param){
          for(let i =0;i<IEMSConfigInfo.value.config.com_link_param.length;i++){
            if(IEMSConfigInfo.value.config.com_link_param[i].type === type){
              if(comList.indexOf(IEMSConfigInfo.value.config.com_link_param[i].tty_channel) === -1){
                items.push({
                  text:IEMSConfigInfo.value.config.com_link_param[i].com_name,
                  value:IEMSConfigInfo.value.config.com_link_param[i].tty_channel,
                })
              }          
            }
          }
        }
        return {
          label: templateParam.label,
          name: templateParam.name,
          defaultValue: templateParam.defaultValue || '',
          value: existingParam ? existingParam.value : (templateParam.defaultValue || ''),
          level: templateParam.level, // 保留level字段
          inputType: templateParam.inputType || 'input', // 保留inputType字段
          items: items, // 保留items字段，用于select类型的选项
          visible: templateParam.visible !== undefined ? templateParam.visible : true // 保留visible字段，默认为true
        }
      }
      else{
        return {
          label: templateParam.label,
          name: templateParam.name,
          defaultValue: templateParam.defaultValue || '',
          value: existingParam ? existingParam.value : (templateParam.defaultValue || ''),
          level: templateParam.level, // 保留level字段
          inputType: templateParam.inputType || 'input', // 保留inputType字段
          items: templateParam.items || [], // 保留items字段，用于select类型的选项
          visible: templateParam.visible !== undefined ? templateParam.visible : true // 保留visible字段，默认为true
        };
      }
      
      
    });
  }
  
  // 显示编辑对话框
  editInstanceDialog.visible = true;
};

// 确认编辑协议实例
const confirmEditInstance = () => {
  if (!editInstanceForm.name) {
    ElMessage.warning('请输入实例名称');
    return;
  }
  
  // 查找父节点
  const parentNode = protocolList.value.find(item => item.id === editInstanceForm.parentId);
  
  if (!parentNode) {
    ElMessage.error('未找到对应的协议原型');
    return;
  }
  
  // 检查同一协议原型下是否已存在相同名称的实例（排除自身）
  const existingInstance = parentNode.children.find((instance: any) => 
    instance.name === editInstanceForm.name && instance.id !== editInstanceForm.id
  );
  
  if (existingInstance) {
    ElMessage.warning(`协议原型 "${parentNode.name}" 下已存在名为 "${editInstanceForm.name}" 的实例`);
    return;
  }
  
  // 查找并更新实例
  const instanceIndex = parentNode.children.findIndex((instance: any) => instance.id === editInstanceForm.id);
  
  if (instanceIndex !== -1) {
    // 保存旧名称用于提示
    const oldName = parentNode.children[instanceIndex].name;
    
    // 准备协议参数，保持原始结构并更新value
    const protocolParams = editInstanceForm.channelParams.map((param: any) => ({
      label: param.label,
      name: param.name,
      defaultValue: param.defaultValue || '',
      value: param.value || '',
      level: param.level, // 保留level字段
      inputType: param.inputType || 'input', // 保留inputType字段
      items: param.items || [], // 保留items字段
      visible: param.visible !== undefined ? param.visible : true // 保留visible字段
    }));
    
    // 更新实例名称和协议参数
    parentNode.children[instanceIndex].name = editInstanceForm.name;
    parentNode.children[instanceIndex].protocol = protocolParams;
    
    editInstanceDialog.visible = false;
    ElMessage.success(`编辑协议实例 "${oldName}" 成功`);
    
    // 标记配置已修改
    markConfigModified();
    
    // 刷新树节点
    nextTick(() => {
      if (protocolTreeRef.value) {
        protocolTreeRef.value.setCurrentKey(editInstanceForm.id);
      }
    });
  }
};

// 处理编辑设备
const handleEditDevice = () => {
  deviceContextMenu.visible = false;
  
  if (!deviceContextMenu.node) {
    ElMessage.warning('请先选择一个设备');
    return;
  }
  
  // 查找父节点（协议实例）和祖父节点（协议原型）
  let parentNode: any = null;
  let parentPrototype: any = null;

  // 遍历协议列表查找实例节点
  for (const prototype of protocolList.value) {
    if (prototype.children) {
      parentNode = prototype.children.find((instance: any) => {
        for(let i =0;i<instance.children.length;i++){
          if(instance.children[i].id === deviceContextMenu.node.id) return instance
        }
      })
      if (parentNode) {
        parentPrototype = prototype;
        break;
      }
    }
  }
  
  if (!parentNode) {
    ElMessage.error('未找到对应的协议实例');
    return;
  }
  
  // 查找设备节点
  const deviceNode = parentNode.children.find((device: any) => device.id === deviceContextMenu.node.id);
  
  if (!deviceNode) {
    ElMessage.error('未找到对应的设备');
    return;
  }
  
  // 填充表单
  editDeviceForm.id = deviceNode.id;
  editDeviceForm.name = deviceNode.name;
  editDeviceForm.instanceName = parentNode.name;
  editDeviceForm.parentId = parentNode.id;
  
  // 添加设备参数
  editDeviceForm.protocol = deviceNode.protocol || [];
  editDeviceForm.deviceParams = [];
  
  // 查找对应的协议模板
  const template = protocolTemplates.value.find(t => t.name === parentPrototype.name);
  
  if (template && template.device && Array.isArray(template.device)) {
    // 设置device参数，结合模板和已有的参数值
    editDeviceForm.deviceParams = template.device.map((templateParam: any) => {
      // 查找设备中已有的参数
      const existingParam = deviceNode.protocol ? 
        deviceNode.protocol.find((p: any) => p.name === templateParam.name) : null;
      
      return {
        label: templateParam.label,
        name: templateParam.name,
        defaultValue: templateParam.defaultValue || '',
        value: existingParam ? existingParam.value : (templateParam.defaultValue || ''),
        level: templateParam.level, // 保留level字段
        inputType: templateParam.inputType || 'input', // 保留inputType字段
        items: templateParam.items || [], // 保留items字段，用于select类型的选项
        visible: templateParam.visible !== undefined ? templateParam.visible : true // 保留visible字段，默认为true
      };
    });
    
    console.log('编辑设备参数:', editDeviceForm.deviceParams); // 调试日志
  }
  
  // 显示编辑对话框
  editDeviceDialog.visible = true;
};

// 确认编辑设备
const confirmEditDevice = () => {
  if (!editDeviceForm.name) {
    ElMessage.warning('请输入设备名称');
    return;
  }
  
  // 查找父节点
  let parentNode: any = null;
  
  // 遍历协议列表查找实例节点
  for (const prototype of protocolList.value) {
    if (prototype.children) {
      parentNode = prototype.children.find((instance: any) => instance.id === editDeviceForm.parentId);
      if (parentNode) break;
    }
  }
  
  if (!parentNode) {
    ElMessage.error('未找到对应的协议实例');
    return;
  }
  
  // 检查同一协议实例下是否已存在相同名称的设备（排除自身）
  const existingDevice = parentNode.children && parentNode.children.some((device: any) => 
    device.name === editDeviceForm.name && device.id !== editDeviceForm.id
  );
  
  if (existingDevice) {
    ElMessage.warning(`协议实例 "${parentNode.name}" 下已存在名为 "${editDeviceForm.name}" 的设备`);
    return;
  }
  
  // 查找并更新设备
  const deviceIndex = parentNode.children.findIndex((device: any) => device.id === editDeviceForm.id);
  
  if (deviceIndex !== -1) {
    // 保存旧名称用于提示
    const oldName = parentNode.children[deviceIndex].name;
    
    // 准备设备参数，保持原始结构并更新value
    const deviceProtocolParams = editDeviceForm.deviceParams.map((param: any) => ({
      label: param.label,
      name: param.name,
      defaultValue: param.defaultValue === 0 ? param.defaultValue :(param.defaultValue || ''),
      value: param.value === 0 ? param.value :(param.value || ''),
      level: param.level, // 保留level字段
      inputType: param.inputType || 'input', // 保留inputType字段
      items: param.items || [], // 保留items字段
      visible: param.visible !== undefined ? param.visible : true // 保留visible字段
    }));
    
    console.log('更新设备参数:', deviceProtocolParams); // 调试日志
    
    // 更新设备名称和协议参数
    parentNode.children[deviceIndex].name = editDeviceForm.name;
    parentNode.children[deviceIndex].protocol = deviceProtocolParams;
    
    editDeviceDialog.visible = false;
    ElMessage.success(`编辑设备 "${oldName}" 成功`);
    
    // 标记配置已修改
    markConfigModified();
    
    // 刷新树节点
    nextTick(() => {
      if (protocolTreeRef.value) {
        protocolTreeRef.value.setCurrentKey(editDeviceForm.id);
        
        // 如果当前选中的是被编辑的设备，更新当前选中的设备
        if (currentProtocol.value && currentProtocol.value.id === editDeviceForm.id) {
          currentProtocol.value = parentNode.children[deviceIndex];
        }
      }
    });
  }
};

// 新建组态模板对话框
const createTemplateDialog = reactive({
  visible: false
});

// 新建模板表单
const createTemplateForm = reactive({
  name: ''
});

// 生成唯一ID
const generateUniqueId = () => {
  // 使用时间戳前缀确保更好的可读性和排序
  const timestamp = new Date().getTime().toString().slice(-6);
  // 生成8位随机ID
  const randomId = generateRandomId();
  // 组合成唯一ID
  return `template_${timestamp}_${randomId}`;
};

// 添加一个变量来存储用户想要执行的操作
const pendingOperation = ref<string | null>(null);

// 检查是否有可用的组态模板，如果没有则弹出创建模板对话框
const checkAndCreateTemplateIfNeeded = (operation: string = '') => {
  if (!configTemplates.value || configTemplates.value.length === 0 || !selectedTemplateId.value) {
    ElMessage.info('请先创建一个组态模板');
    // 记录用户想要执行的操作
    pendingOperation.value = operation;
    // 清空表单
    createTemplateForm.name = '';
    // 显示创建模板对话框
    createTemplateDialog.visible = true;
    return false;
  }
  return true;
};

// 处理新建组态模板
const handleCreateTemplate = () => {
  createTemplateForm.name = ''; // 清空表单
  createTemplateDialog.visible = true;
};

// 确认新建组态模板
const confirmCreateTemplate = () => {
  // 验证模板名称
  if (!createTemplateForm.name.trim()) {
    ElMessage.warning('请输入模板名称');
    return;
  }
  
  // 生成唯一ID
  let newId = generateRandomId(); // 使用8位随机ID
  
  // 确保ID唯一
  while (configTemplates.value.some(template => template.id === newId)) {
    newId = generateRandomId();
  }
  
  // 创建新的空组态模板
  const newTemplate = {
    id: newId,
    name: createTemplateForm.name.trim(),
    link: {} // 初始化为空对象
  };
  
  // 添加到模板列表
  configTemplates.value.push(newTemplate);
  
  // 选中新创建的模板
  selectedTemplateId.value = newTemplate.id;
  configurationId.value = newTemplate.id;
  
  // 解析新模板 - 此时是空的
  parseConfigurationTemplate(newTemplate);
  
  // 标记配置已修改
  markConfigModified();
  
  createTemplateDialog.visible = false;
  ElMessage.success(`成功创建组态模板 "${newTemplate.name}"`);
  
  // 如果有待执行的操作，根据操作类型继续执行
  if (pendingOperation.value) {
    nextTick(() => {
      switch (pendingOperation.value) {
        case 'addPrototype':
          prototypeForm.type = '';
          prototypeDialog.visible = true;
          break;
        case 'addInstance':
          // 由于实例需要选择协议原型，这里不能直接打开对话框
          ElMessage.info('请先选择一个协议原型，然后右键添加实例');
          break;
        case 'addDevice':
          // 由于设备需要选择协议实例，这里不能直接打开对话框
          ElMessage.info('请先选择一个协议实例，然后右键添加设备');
          break;
        case 'addDeviceFromTemplate':
          // 由于从模板添加设备需要选择协议实例，这里不能直接打开对话框
          ElMessage.info('请先选择一个协议实例，然后右键从模板添加设备');
          break;
        case 'addPoint':
          // 由于点位需要选择设备，这里不能直接打开对话框
          ElMessage.info('请先选择一个设备，然后添加位号');
          break;
        default:
          break;
      }
    });
    // 清除待执行的操作
    pendingOperation.value = null;
  }
};

// 编辑组态模板对话框
const editTemplateDialog = reactive({
  visible: false
});

// 编辑模板表单
const editTemplateForm = reactive({
  id: '',
  name: ''
});

// 处理编辑组态模板
const handleEditTemplate = (template: any) => {
  // 阻止事件冒泡，防止触发选择操作
  event?.stopPropagation();
  
  // 检查是否是当前选中的模板
  if (template.id !== selectedTemplateId.value) {
    ElMessage.warning('只能编辑当前选中的模板');
    return;
  }
  
  // 填充表单数据
  editTemplateForm.id = template.id;
  editTemplateForm.name = template.name;
  
  // 显示编辑对话框
  editTemplateDialog.visible = true;
};

// 确认编辑组态模板
const confirmEditTemplate = () => {
  // 验证模板名称
  if (!editTemplateForm.name.trim()) {
    ElMessage.warning('请输入模板名称');
    return;
  }
  
  // 查找要编辑的模板
  const templateIndex = configTemplates.value.findIndex(item => item.id === editTemplateForm.id);
  if (templateIndex === -1) {
    ElMessage.error('未找到要编辑的模板');
    return;
  }
  
  // 保存旧名称用于提示
  const oldName = configTemplates.value[templateIndex].name;
  
  // 更新模板名称
  configTemplates.value[templateIndex].name = editTemplateForm.name.trim();
  
  // 如果修改的是当前选中的模板，更新当前模板的名称和相关数据
  if (selectedTemplateId.value === editTemplateForm.id) {
    // 更新当前模板的名称，确保保存时使用正确的名称
    const currentTemplate = configTemplates.value[templateIndex];
    
    // 更新convertToConfigJSON函数中使用的模板名称
    if (currentTemplate) {
      // 确保在保存时使用更新后的名称
      currentTemplate.name = editTemplateForm.name.trim();
    }
  }
  
  // 关闭对话框
  editTemplateDialog.visible = false;
  
  // 显示成功消息
  ElMessage.success(`模板名称已更新为 "${editTemplateForm.name.trim()}"`);
  
  // 标记配置已修改
  markConfigModified();
};

// 组件挂载时获取设备模板列表和组态配置模板
// onMounted(() => {
//   // 防止重复初始化
//   if (!hasInitialized.value) {
//     // 获取组态配置模板
//     fetchConfigurationTemplate();
    
//     // 获取设备模板列表
//     fetchDeviceTemplates();
    
//     // 获取协议模板列表
//     fetchConfigLinkTemplate();
    
//     // 标记为已初始化
//     hasInitialized.value = true;
//   }
  
//   // 添加全局点击事件监听器，确保任何点击都能关闭右键菜单
//   document.addEventListener('click', () => {
//     contextMenu.visible = false;
//     nodeContextMenu.visible = false;
//     instanceContextMenu.visible = false;
//     deviceContextMenu.visible = false;
//     pointContextMenu.visible = false;
//   });
// });

// 处理删除组态模板
const handleDeleteTemplate = (template: any) => {
  // 阻止事件冒泡，防止触发选择操作
  event?.stopPropagation();
  
  // 检查是否是当前选中的模板
  if (template.id !== selectedTemplateId.value) {
    ElMessage.warning('只能删除当前选中的模板');
    return;
  }
  
  ElMessageBox.confirm(
    `确定要删除组态模板 "${template.name}" 吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        // 显示加载状态
        const loading = ElLoading.service({
          lock: true,
          text: '正在删除组态模板...',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        
        // 调用删除API
        const res = await deleteConfigurationTemplate(template);
        
        // 关闭加载状态
        loading.close();
        
        // 检查API响应
        if (res.result && res.result.resultCode === "0") {
          ElMessage.success(`删除组态模板 "${template.name}" 成功`);
          
          // 删除成功后重新加载组态模板列表以同步数据
          await fetchConfigurationTemplate();
          
          // 如果删除的是当前选中的模板，需要重新选择模板
          if (selectedTemplateId.value === template.id) {
            if (configTemplates.value.length > 0) {
              // 切换到第一个可用的模板
              selectedTemplateId.value = configTemplates.value[0].id;
              // 重新解析选中的模板
              parseConfigurationTemplate(configTemplates.value[0]);
              console.log('删除模板后重新加载的协议列表:', protocolList.value);
            } else {
              // 如果没有可用的模板，清空当前选中
              selectedTemplateId.value = '';
              protocolList.value = [];
              currentProtocol.value = null;
              pointsList.value = [];
              
              // 重置修改状态
              isConfigModified.value = false;
              originalConfig.value = null; // 确保原始配置也被清空
              
              ElMessage.info('没有可用的组态模板，请创建新模板');
            }
          }
        } else {
          // 如果code不是"0"，则表示失败
          ElMessage.error(`删除失败: ${res.result?.resultError || '未知错误'}`);
        }
      } catch (error) {
        console.error('删除失败:', error);
        ElMessage.error('删除组态模板失败');
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 处理表格空白处右键菜单
const handleTableContextMenu = (event: MouseEvent) => {
  event.preventDefault();
  event.stopPropagation();
  
  // 如果当前没有选中设备，不显示菜单
  if (!currentProtocol.value || currentProtocol.value.type !== 'device') {
    return;
  }
  
  // 隐藏所有右键菜单
  contextMenu.visible = false;
  nodeContextMenu.visible = false;
  instanceContextMenu.visible = false;
  deviceContextMenu.visible = false;
  
  // 显示点位右键菜单（空白处右键时没有选中行数据）
  pointContextMenu.x = event.clientX;
  pointContextMenu.y = event.clientY;
  pointContextMenu.data = null;
  pointContextMenu.visible = true;
};

// 添加内联编辑状态
const inlineEditingState = reactive({
  row: null as any,
  field: '',
  value: '',
  isEditing: false
});

// 处理单元格双击编辑
const handleCellDbClick = (row: any, field: string) => {
  // 检查字段类型，决定使用输入框还是下拉选择框
  let fieldConfig: any = null;
  if (field === 'address') {
    fieldConfig = pointAddressConfig.value;
  } else if (field === 'type') {
    fieldConfig = pointTypeConfig.value;
  }
  
  // 如果字段的 required 为 false，则不允许编辑
  if (fieldConfig && fieldConfig.required === false) {
    ElMessage.info(`${fieldConfig.label || field}字段不需要填写`);
    return;
  }
  
  // 设置编辑状态
  inlineEditingState.row = row;
  inlineEditingState.field = field;
  inlineEditingState.value = row[field] || '';
  inlineEditingState.isEditing = true;
  
  // 创建一个临时输入框
  const cell = event?.target as HTMLElement;
  if (!cell) return;
  
  // 清空原单元格内容
  const originalContent = cell.innerHTML;
  cell.innerHTML = '';
  
  // 如果字段配置为select类型，创建下拉选择框
  if (fieldConfig && fieldConfig.inputType === 'select' && fieldConfig.items) {
    const select = document.createElement('select');
    select.style.width = '100%';
    select.style.height = '100%';
    select.style.border = 'none';
    select.style.outline = 'none';
    select.style.padding = '0 5px';
    select.style.boxSizing = 'border-box';
    select.style.backgroundColor = '#fff';
    select.style.borderRadius = '2px';
    
    // 添加空选项
    const emptyOption = document.createElement('option');
    emptyOption.value = '';
    emptyOption.textContent = `请选择${fieldConfig.label || field}`;
    select.appendChild(emptyOption);
    
    // 添加选项
    fieldConfig.items.forEach((item: any) => {
      const option = document.createElement('option');
      option.value = item.value;
      option.textContent = item.text;
      if (item.value == inlineEditingState.value) {
        option.selected = true;
      }
      select.appendChild(option);
    });
    
    // 添加到单元格
    cell.appendChild(select);
    
    // 聚焦
    select.focus();
    
    // 处理失去焦点事件
    select.addEventListener('blur', () => {
      finishEditingSelect(cell, originalContent, select);
    });
    
    // 处理回车事件
    select.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        finishEditingSelect(cell, originalContent, select);
      } else if (e.key === 'Escape') {
        // 取消编辑，恢复原值
        inlineEditingState.isEditing = false;
        cell.innerHTML = originalContent;
      }
    });
  } else {
    // 创建普通输入框
    const input = document.createElement('input');
    input.value = inlineEditingState.value;
    input.style.width = '100%';
    input.style.height = '100%';
    input.style.border = 'none';
    input.style.outline = 'none';
    input.style.padding = '0 5px';
    input.style.boxSizing = 'border-box';
    input.style.backgroundColor = '#fff';
    input.style.borderRadius = '2px';
    
    // 添加到单元格
    cell.appendChild(input);
    
    // 聚焦并全选
    input.focus();
    input.select();
    
    // 处理失去焦点事件
    input.addEventListener('blur', () => {
      finishEditingInput(cell, originalContent, input);
    });
    
    // 处理回车事件
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        finishEditingInput(cell, originalContent, input);
      } else if (e.key === 'Escape') {
        // 取消编辑，恢复原值
        inlineEditingState.isEditing = false;
        cell.innerHTML = originalContent;
      }
    });
  }
};

// 完成下拉选择框编辑
const finishEditingSelect = (cell: HTMLElement, originalContent: string, select: HTMLSelectElement) => {
  if (!inlineEditingState.isEditing) return;
  
  // 获取选中的值
  const newValue = select.value;
  
  // 处理编辑完成
  processEditingFinish(cell, originalContent, newValue);
};

// 完成输入框编辑
const finishEditingInput = (cell: HTMLElement, originalContent: string, input: HTMLInputElement) => {
  if (!inlineEditingState.isEditing) return;
  
  // 获取输入值
  const newValue = input.value.trim();
  
  // 处理编辑完成
  processEditingFinish(cell, originalContent, newValue);
};

// 处理编辑完成的通用逻辑
const processEditingFinish = (cell: HTMLElement, originalContent: string, newValue: string) => {
  if (!inlineEditingState.isEditing) return;
  
  // 验证字段（如果是code字段）
  if (inlineEditingState.field === 'code') {
    // 验证是否为空
    if (!newValue) {
      // 如果是新行且为空，不显示警告，直接恢复
      if (inlineEditingState.row.isNewRow) {
        cell.innerHTML = originalContent;
        inlineEditingState.isEditing = false;
        return;
      }
      
      ElMessage.warning('位号名称不能为空');
      cell.innerHTML = originalContent;
      inlineEditingState.isEditing = false;
      return;
    }
    
    // 验证格式
    if (!/^[A-Za-z0-9_]+$/.test(newValue)) {
      ElMessage.warning('位号名称只能包含字母、数字和下划线');
      cell.innerHTML = originalContent;
      inlineEditingState.isEditing = false;
      return;
    }
    
    // 验证是否重复
    if (
      currentProtocol.value && 
      currentProtocol.value.points && 
      currentProtocol.value.points.some((p: any) => 
        p.id !== inlineEditingState.row.id && p.code === newValue
      )
    ) {
      ElMessage.warning(`设备 "${currentProtocol.value.name}" 下已存在名称为 "${newValue}" 的点位`);
      cell.innerHTML = originalContent;
      inlineEditingState.isEditing = false;
      return;
    }
  } else if (inlineEditingState.field === 'name' && !newValue) {
    // 如果是新行且为空，不显示警告，直接恢复
    if (inlineEditingState.row.isNewRow) {
      cell.innerHTML = originalContent;
      inlineEditingState.isEditing = false;
      return;
    }
    
    // 验证描述是否为空
          ElMessage.warning('位号描述不能为空');
      cell.innerHTML = originalContent;
      inlineEditingState.isEditing = false;
      return;
  }
  
  // 更新数据
  const row = inlineEditingState.row;
  const field = inlineEditingState.field;
  
  // 检查是否是新行
  if (row.isNewRow) {
            // 对于新行，只有当必填字段都填写后才添加到点位列表
        if (field === 'code' || field === 'name') {
          // 更新当前字段
          row[field] = newValue;
          
             // 检查必填字段（名称和描述）是否都已填写
       if (row.code && row.name) {
         // 创建新点位
        const newPoint = {
          id: generateUUID(),
          code: row.code,
          name: row.name,
          address: row.address || '',
          type: row.type || ''
        };
        
        // 添加到设备的点位列表
        if (currentProtocol.value && currentProtocol.value.type === 'device') {
          if (!currentProtocol.value.points) {
            currentProtocol.value.points = [];
          }
          currentProtocol.value.points.push(newPoint);
          
          // 标记配置已修改
          markConfigModified();
          
          // 重置新行
          row.code = '';
          row.name = '';
          row.address = '';
          row.type = '';
          row.id = 'new_point_' + Date.now();
          
          // 显示成功消息
          ElMessage.success('新点位添加成功');
        }
      }
    } else {
      // 对于非必填字段，直接更新
      row[field] = newValue;
    }
  } else {
    // 对于现有行，检查值是否真的发生了变化
    const oldValue = row[field] || '';
    
    // 只有当值真正发生变化时才更新数据并标记为已修改
    if (newValue !== oldValue) {
      // 更新行数据
      row[field] = newValue;
      
      // 更新原始数据（在设备中找到并更新点位）
      if (currentProtocol.value && currentProtocol.value.points) {
        const pointIndex = currentProtocol.value.points.findIndex((p: any) => p.id === row.id);
        if (pointIndex !== -1) {
          currentProtocol.value.points[pointIndex][field] = newValue;
          
          // 标记配置已修改
          markConfigModified();
        }
      }
    } else {
      // 值没有变化，不需要标记为已修改
      console.log('值未发生变化，不标记为已修改');
    }
  }
  
  // 恢复单元格显示，如果是select类型字段，显示对应的文本
  const displayValue = getDisplayValue(newValue, inlineEditingState.field);
  cell.innerHTML = displayValue || '';
  
  // 重置编辑状态
  inlineEditingState.isEditing = false;
};

// 修改pointsList的计算方式，添加一个空行用于新增
const displayPointsList = computed(() => {
  // 如果没有选中设备或不是设备节点，返回空数组
  if (!currentProtocol.value || currentProtocol.value.type !== 'device') {
    return [];
  }
  
  // 获取当前设备的点位列表
  const points = currentProtocol.value.points || [];
  
  // 添加一个空行用于新增
  const emptyRow = {
    id: 'new_point_' + Date.now(), // 使用时间戳生成临时ID
    code: '',
    name: '',
    address: '',
    type: '',
    isNewRow: true // 标记为新行
  };
  
  // 返回原有点位加上空行
  return [...points, emptyRow];
});
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 100%;
  padding: 0;
  box-sizing: border-box;
  
  .main-card {
    height: 100%;
    
    :deep(.el-card__header) {
      padding: 12px 15px;
    }
    
    :deep(.el-card__body) {
      padding: 10px 15px;
      height: calc(100% - 60px);
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: none;
    padding-bottom: 0;
  }
  
  .header-content {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
  }
  
  .header-buttons {
    display: flex;
    gap: 8px;
  }
  
  .card-content {
    height: 100%;
    
    .content-layout {
      display: flex;
      height: 100%;
      
      .left-panel {
        width: 220px;
        border-right: 1px solid #ebeef5;
        padding: 5px;
        box-sizing: border-box;
        overflow-y: auto;
        position: relative;
        background-color: #f9f9f9;
        border-radius: 4px;
        margin-right: 8px;
        height: 100%;
        
        .list-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          font-weight: bold;
          font-size: 14px;
          padding: 0 5px;
        }
        
        .protocol-tree {
          height: calc(100% - 40px);
          background-color: transparent;
          
          :deep(.el-tree-node__content) {
            height: 32px;
            border-radius: 4px;
            margin-bottom: 2px;
            
            &:hover {
              background-color: #f0f2f5;
            }
          }
        }
      }
      .right-panel {
        flex: 1;
        padding: 5px 0 5px 10px;
        box-sizing: border-box;
        overflow: hidden;
        position: relative;
        background-color: #fff;
        border-radius: 4px;
        height: calc(100% + 1px);
        margin-top: -1px;
        
        .table-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          padding: 0 5px;
          
          .table-title {
            font-weight: bold;
            font-size: 14px;
          }
          
          .table-tip {
            color: #909399;
            font-size: 12px;
            font-style: italic;
          }
        }
        
        :deep(.el-table) {
          height: 100%;
          border-radius: 4px;
          overflow: hidden;
          margin-top: 0;
          
          .el-table__header th {
            font-size: 14px;
            background-color: #f5f7fa;
            height: 45px;
            font-weight: 600;
          }
          
          .el-table__row {
            height: 40px;
          }
          
          .el-table--striped .el-table__body tr.el-table__row--striped td {
            background-color: #f9fafc;
          }
        }
      }
    }
  }

  /* 右键菜单样式 */
  .context-menu {
    position: fixed;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 4px 0;
    z-index: 2000;
    
    .context-menu-item {
      padding: 6px 12px;
      cursor: pointer;
      font-size: 14px;
      
      &:hover {
        background-color: #f5f7fa;
        color: var(--el-color-primary);
      }
    }
  }

  /* 树节点样式 */
  .custom-tree-node {
    width: 100%;
    display: flex;
    align-items: center;
    
    &.node-selected {
      color: var(--el-color-primary);
      font-weight: bold;
    }
  }
}

/* 添加到现有样式中 */
.template-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.template-option span {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-actions {
  display: flex;
  align-items: center;
}

.template-actions .el-button {
  margin-left: 5px;
}

.template-points-count {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

.has-changes {
  position: relative;
}

.changes-badge {
  margin-left: 8px;
}

:deep(.el-badge__content:not(.is-dot)) {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
  padding: 0 6px;
  border-radius: 10px;
}

.template-selector {
  display: flex;
  align-items: center;
}

.dialog-tip {
  color: #909399;
  font-size: 12px;
  margin-top: -10px;
  
  p {
    margin: 0 0 8px 0;
    font-weight: 500;
    color: #606266;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 4px;
      line-height: 1.4;
    }
  }
}

/* 修改下拉选项的样式，使按钮靠最右 */
:deep(.el-select-dropdown__item) {
  padding-right: 10px !important;
}

:deep(.el-select-dropdown__item .template-option) {
  margin-right: -5px;
}

.editable-cell {
  cursor: pointer;
  padding: 5px;
  width: 100%;
  height: 100%;
  
  &:hover {
    background-color: #f5f7fa;
  }
  
  &.disabled-field {
    cursor: not-allowed;
    background-color: #f5f7fa;
    color: #909399;
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    .disabled-text {
      font-style: italic;
      color: #c0c4cc;
    }
  }
}

/* 预览部分样式 */
.preview-section {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

.preview-header {
  margin-bottom: 10px;
}

.preview-title {
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}
</style> 

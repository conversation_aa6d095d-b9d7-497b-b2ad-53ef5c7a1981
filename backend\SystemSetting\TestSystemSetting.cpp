#include "stdio.h"
#include <iostream>
#include "ISystemSetting.h"

#define PRINTF_NAME_VALUE(X) {std::cout<<#X<<"   "<<X<<std::endl;}

void testGetNetworkConfig(ISystemSetting* pobj, std::string name)
{
	SNetworkConfig nc;
	pobj->getNetworkConfig(name, nc);
	PRINTF_NAME_VALUE(nc.type);
	PRINTF_NAME_VALUE(nc.name);
	PRINTF_NAME_VALUE(nc.netmask);
	PRINTF_NAME_VALUE(nc.ipv4);
	PRINTF_NAME_VALUE(nc.mac);
	PRINTF_NAME_VALUE(nc.ipv6);
	PRINTF_NAME_VALUE(nc.rxPackets);
	PRINTF_NAME_VALUE(nc.rxBytes);
	PRINTF_NAME_VALUE(nc.txPackets);
	PRINTF_NAME_VALUE(nc.txBytes);
}

void testGetNetworkConfigList(ISystemSetting* pobj)
{
	NetworkConfigList ncl;
	pobj->getNetworkConfigList(ncl);
	for(auto& nc:ncl)
	{
		std::cout<<"-------------------"<<std::endl;
		PRINTF_NAME_VALUE(nc.type);
		PRINTF_NAME_VALUE(nc.name);
		PRINTF_NAME_VALUE(nc.netmask);
		PRINTF_NAME_VALUE(nc.ipv4);
		PRINTF_NAME_VALUE(nc.mac);
		PRINTF_NAME_VALUE(nc.ipv6);
		PRINTF_NAME_VALUE(nc.rxPackets);
		PRINTF_NAME_VALUE(nc.rxBytes);
		PRINTF_NAME_VALUE(nc.txPackets);
		PRINTF_NAME_VALUE(nc.txBytes);
	}
}

void testGetMemoryInfo(ISystemSetting* pobj)
{
	SMemoryInfo mi;
	pobj->getMemoryInfo(mi);
	PRINTF_NAME_VALUE(mi.memTotal);
	PRINTF_NAME_VALUE(mi.memUsed);
	PRINTF_NAME_VALUE(mi.memFree);
	PRINTF_NAME_VALUE(mi.swapTotal);
	PRINTF_NAME_VALUE(mi.swapUsed);
	PRINTF_NAME_VALUE(mi.swapFree);
}

void testGetCPUInfo(ISystemSetting* pobj)
{
	SCPUInfo cif;
	pobj->getCPUInfo(cif);
}

void testGetCurrentStorageInfo(ISystemSetting* pobj)
{
	SStorageInfo sif;
	pobj->getCurrentStorageInfo(sif);
}

int main(int argc, char** argv)
{
	ISystemSetting* pobj = create();
	//pobj->init("Linux-A40i");
	pobj->init("General");
	std::cout << "\033c"<<std::endl;

	// testGetMemoryInfo(pobj);
	// testGetNetworkConfig(pobj, "ens33");
	// testGetNetworkConfigList(pobj);
	// testGetCPUInfo(pobj);
	testGetCurrentStorageInfo(pobj);

	destory(pobj);
	return 0;
}
